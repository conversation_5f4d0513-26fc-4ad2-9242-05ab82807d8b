# Next.js API Implementation Guide - Order Voice Backend

## Overview for AI Developer

This guide provides step-by-step instructions for building a Next.js API system that replicates the FastAPI Order Voice Backend. Each API endpoint must maintain identical functionality, request/response formats, and business logic.

## Project Structure

```
/pages/api/
├── auth/
│   ├── signin.js
│   └── signup.js
├── transcription/
│   ├── process.js
│   └── analyze.js
├── orders/
│   └── create-order.js
├── products/
│   ├── create-product.js
│   ├── create-product-with-units.js
│   └── find-products.js
├── customers/
│   ├── create-customer.js
│   └── find-customers.js
├── suppliers/
│   ├── create-supplier.js
│   └── find-suppliers.js
├── import-slips/
│   ├── create-import-slip.js
│   └── create-delivery-note.js
├── unit-conversions/
│   ├── create-unit-conversion.js
│   └── list.js
├── invoices/
│   └── generate.js
└── plan-status/
    └── get-status-plan.js
```

## Environment Variables (.env.local)

```env
# Teable API Configuration
TEABLE_BASE_URL=https://app.teable.vn/api
TEABLE_TOKEN=Bearer teable_acc...
TEABLE_TABLE_ID=tblj52nsIFcIWDAW4fr
TEABLE_USER_VIEW_ID=viwWOH429ek2bW3eU06
TEABLE_TOKEN_LIST_TABLE_ID=tblR7dckuSizsZlhW47
PLAN_STATUS_TABLE_ID=tblL2pLkyLQgPzmCVHU

# Application Configuration
JWT_SECRET_KEY=CUBABLE_JWT_SECRET_2025
VIETQR_API_BASE_URL=https://api.vietqr.io/v2/business
```

## Core Utilities to Create

### 1. Teable API Client (`/lib/teable.js`)

```javascript
export class TeableClient {
  constructor() {
    this.baseURL = process.env.TEABLE_BASE_URL;
    this.token = process.env.TEABLE_TOKEN;
  }

  async makeRequest(method, endpoint, data = null, params = {}) {
    const url = new URL(`${this.baseURL}${endpoint}`);
    Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));

    const options = {
      method,
      headers: {
        'Authorization': this.token,
        'Content-Type': 'application/json',
      },
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(url, options);
    return await response.json();
  }

  // GET records from table
  async getRecords(tableId, params = {}) {
    return this.makeRequest('GET', `/table/${tableId}/record`, null, params);
  }

  // POST create record
  async createRecord(tableId, data) {
    return this.makeRequest('POST', `/table/${tableId}/record`, data);
  }

  // PATCH update record
  async updateRecord(tableId, recordId, data) {
    return this.makeRequest('PATCH', `/table/${tableId}/record/${recordId}`, data);
  }

  // GET single record
  async getRecord(tableId, recordId, params = {}) {
    return this.makeRequest('GET', `/table/${tableId}/record/${recordId}`, null, params);
  }
}
```

### 2. Authentication Utilities (`/lib/auth.js`)

```javascript
import jwt from 'jsonwebtoken';
import crypto from 'crypto';

// Password encoding with private rules (replicate FastAPI logic)
export function encodePassword(password, username) {
  const salt = username + 'CUBABLE_SALT_2025';
  const hash = crypto.createHmac('sha256', process.env.JWT_SECRET_KEY)
    .update(password + salt)
    .digest('base64');
  return `CUBABLE_${hash}_ENCODED`;
}

// JWT token generation
export function generateJWT(payload) {
  return jwt.sign(payload, process.env.JWT_SECRET_KEY, { expiresIn: '24h' });
}

// JWT token verification
export function verifyJWT(token) {
  try {
    return jwt.verify(token, process.env.JWT_SECRET_KEY);
  } catch (error) {
    return null;
  }
}

// Get user from token
export async function getCurrentUser(req) {
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (!token) return null;
  
  const decoded = verifyJWT(token);
  if (!decoded) return null;
  
  return decoded.username;
}
```

### 3. Credit Management (`/lib/credit.js`)

```javascript
import { TeableClient } from './teable.js';

export async function reduceCreditValue(username) {
  const teable = new TeableClient();
  
  try {
    // Step 1: Get user's current_plan ID
    const userParams = {
      fieldKeyType: 'dbFieldName',
      viewId: process.env.TEABLE_USER_VIEW_ID,
      filter: JSON.stringify({
        conjunction: 'and',
        filterSet: [{ fieldId: 'username', operator: 'is', value: username }]
      })
    };
    
    const userResult = await teable.getRecords(process.env.TEABLE_TABLE_ID, userParams);
    if (!userResult.records?.length) return false;
    
    const currentPlan = userResult.records[0].fields.current_plan;
    if (!currentPlan) return false;
    
    const planStatusId = Array.isArray(currentPlan) ? currentPlan[0] : currentPlan;
    
    // Step 2: Get current credit_value
    const planResult = await teable.getRecord(
      process.env.PLAN_STATUS_TABLE_ID, 
      planStatusId, 
      { fieldKeyType: 'dbFieldName' }
    );
    
    const currentCredit = planResult.fields?.credit_value || 0;
    if (currentCredit <= 0) return false;
    
    // Step 3: Reduce credit by 1
    const updateData = {
      fieldKeyType: 'dbFieldName',
      record: {
        fields: {
          credit_value: currentCredit - 1
        }
      }
    };
    
    await teable.updateRecord(process.env.PLAN_STATUS_TABLE_ID, planStatusId, updateData);
    return true;
    
  } catch (error) {
    console.error('Credit reduction error:', error);
    return false;
  }
}
```

### 4. VietQR Integration (`/lib/vietqr.js`)

```javascript
export async function validateTaxCode(taxcode) {
  try {
    const response = await fetch(`${process.env.VIETQR_API_BASE_URL}/${taxcode}`);
    const data = await response.json();
    
    if (response.ok && data.data) {
      return {
        valid: true,
        business_name: data.data.name,
        address: data.data.address
      };
    }
    
    return { valid: false, error: 'Invalid taxcode' };
  } catch (error) {
    return { valid: false, error: error.message };
  }
}
```

## API Implementation Details

### 1. Authentication APIs

#### `/pages/api/auth/signin.js`

```javascript
import { TeableClient } from '../../../lib/teable.js';
import { encodePassword, generateJWT } from '../../../lib/auth.js';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { username, password } = req.body;
  
  try {
    const teable = new TeableClient();
    const encodedPassword = encodePassword(password, username);
    
    // Find user with matching username and password
    const params = {
      fieldKeyType: 'dbFieldName',
      viewId: process.env.TEABLE_USER_VIEW_ID,
      filter: JSON.stringify({
        conjunction: 'and',
        filterSet: [
          { fieldId: 'username', operator: 'is', value: username },
          { fieldId: 'password', operator: 'is', value: encodedPassword }
        ]
      })
    };
    
    const result = await teable.getRecords(process.env.TEABLE_TABLE_ID, params);
    
    if (!result.records?.length) {
      return res.status(401).json({
        status: 'error',
        detail: 'Thông tin đăng nhập không chính xác'
      });
    }
    
    const user = result.records[0];
    
    // Generate JWT token
    const token = generateJWT({
      username: user.fields.username,
      role: user.fields.role || 'user',
      permissions: ['read', 'write']
    });
    
    // Update last_login
    await teable.updateRecord(process.env.TEABLE_TABLE_ID, user.id, {
      fieldKeyType: 'dbFieldName',
      record: {
        fields: {
          last_login: new Date().toISOString()
        }
      }
    });
    
    // Store token in registry
    await teable.createRecord(process.env.TEABLE_TOKEN_LIST_TABLE_ID, {
      fieldKeyType: 'dbFieldName',
      records: [{
        fields: {
          username: username,
          token: token
        }
      }]
    });
    
    return res.status(200).json({
      status: 'success',
      accessToken: token,
      detail: 'Đăng nhập thành công',
      record: [user.fields]
    });
    
  } catch (error) {
    return res.status(500).json({
      status: 'error',
      detail: `Lỗi máy chủ: ${error.message}`
    });
  }
}
```

#### `/pages/api/auth/signup.js`

```javascript
import { TeableClient } from '../../../lib/teable.js';
import { validateTaxCode } from '../../../lib/vietqr.js';
import { encodePassword, generateJWT } from '../../../lib/auth.js';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { username, password } = req.body; // username is taxcode
  
  try {
    // Step 1: Validate taxcode with VietQR
    const vietqrResult = await validateTaxCode(username);
    if (!vietqrResult.valid) {
      return res.status(400).json({
        status: 'error',
        detail: 'Mã số thuế không hợp lệ'
      });
    }
    
    const teable = new TeableClient();
    
    // Step 2: Check if user already exists
    const existingUser = await teable.getRecords(process.env.TEABLE_TABLE_ID, {
      fieldKeyType: 'dbFieldName',
      filter: JSON.stringify({
        conjunction: 'and',
        filterSet: [{ fieldId: 'username', operator: 'is', value: username }]
      })
    });
    
    if (existingUser.records?.length) {
      return res.status(400).json({
        status: 'error',
        detail: 'Tài khoản đã tồn tại'
      });
    }
    
    // Step 3: Create Teable workspace (space + base)
    const workspace = await createTeableWorkspace(username);
    
    // Step 4: Create all required tables
    const tables = await createAllTables(workspace.base_id, workspace.access_token);
    
    // Step 5: Create user record
    const encodedPassword = encodePassword(password, username);
    const userPayload = {
      fieldKeyType: 'dbFieldName',
      records: [{
        fields: {
          username: username,
          password: encodedPassword,
          business_name: vietqrResult.business_name,
          space_token: workspace.access_token,
          ...tables // All table IDs
        }
      }]
    };
    
    const userResult = await teable.createRecord(process.env.TEABLE_TABLE_ID, userPayload);
    
    // Step 6: Generate JWT token
    const token = generateJWT({
      username: username,
      role: 'user',
      permissions: ['read', 'write']
    });
    
    return res.status(200).json({
      status: 'success',
      detail: 'Đăng ký thành công',
      account_id: userResult.records[0].id,
      business_name: vietqrResult.business_name,
      workspace: workspace,
      tables: tables
    });
    
  } catch (error) {
    return res.status(500).json({
      status: 'error',
      detail: `Lỗi máy chủ: ${error.message}`
    });
  }
}

// Helper function to create Teable workspace
async function createTeableWorkspace(username) {
  // Implementation for creating space and base in Teable
  // Returns { space_id, base_id, access_token }
}

// Helper function to create all tables
async function createAllTables(baseId, token) {
  // Implementation for creating all required tables
  // Returns object with all table IDs
}
```

### 2. Order Management APIs

#### `/pages/api/orders/create-order.js`

```javascript
import { TeableClient } from '../../../lib/teable.js';
import { getCurrentUser } from '../../../lib/auth.js';
import { reduceCreditValue } from '../../../lib/credit.js';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const currentUser = await getCurrentUser(req);
    if (!currentUser) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { customer_id, order_details, delivery_type } = req.body;
    const teable = new TeableClient();
    
    // Step 1: Get user table information
    const userInfo = await getUserTableInfo(currentUser);
    
    // Step 2: Create order details
    const orderDetailsPayload = {
      fieldKeyType: 'dbFieldName',
      records: order_details.map(detail => ({
        fields: {
          product_link: [detail.product_id],
          unit_conversions: [detail.unit_conversions_id],
          unit_price: detail.unit_price,
          quantity: detail.quantity,
          vat: detail.vat,
          temp_total: detail.unit_price * detail.quantity,
          final_total: (detail.unit_price * detail.quantity) * (1 + detail.vat / 100)
        }
      }))
    };
    
    const detailsResult = await teable.createRecord(
      userInfo.table_order_detail_id, 
      orderDetailsPayload
    );
    
    // Step 3: Create main order
    const orderPayload = {
      fieldKeyType: 'dbFieldName',
      records: [{
        fields: {
          customer_link: [customer_id],
          invoice_details: detailsResult.records.map(r => r.id),
          delivery_type: delivery_type
        }
      }]
    };
    
    const orderResult = await teable.createRecord(userInfo.table_order_id, orderPayload);
    const orderId = orderResult.records[0].id;
    
    // Step 4: Create delivery note automatically
    const deliveryNoteResult = await createDeliveryNote(orderId, customer_id, userInfo);
    
    // Step 5: Reduce credit value
    const creditReduced = await reduceCreditValue(currentUser);
    
    // Step 6: Calculate totals
    const totals = calculateOrderTotals(order_details);
    
    return res.status(200).json({
      status: 'success',
      detail: 'Tạo đơn hàng thành công',
      order_id: orderId,
      order_code: `ORD-${orderId}`,
      delivery_note_id: deliveryNoteResult.id,
      delivery_note_code: `DN-${deliveryNoteResult.id}`,
      customer_id: customer_id,
      ...totals
    });
    
  } catch (error) {
    return res.status(500).json({
      status: 'error',
      detail: `Lỗi tạo đơn hàng: ${error.message}`
    });
  }
}

// Helper functions
async function getUserTableInfo(username) {
  // Get user's table IDs from user record
}

async function createDeliveryNote(orderId, customerId, userInfo) {
  // Create delivery note automatically
}

function calculateOrderTotals(orderDetails) {
  // Calculate total_items, total_temp, total_vat, total_after_vat
}
```

### 3. Product Management APIs

#### `/pages/api/products/create-product.js`

```javascript
import { TeableClient } from '../../../lib/teable.js';
import { getCurrentUser } from '../../../lib/auth.js';
import { reduceCreditValue } from '../../../lib/credit.js';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const currentUser = await getCurrentUser(req);
    if (!currentUser) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { product_name, unit_conversions } = req.body;
    const teable = new TeableClient();
    
    // Get user table info
    const userInfo = await getUserTableInfo(currentUser);
    
    // Create product
    const productPayload = {
      fieldKeyType: 'dbFieldName',
      records: [{
        fields: {
          product_name: product_name,
          unit_conversions: unit_conversions // Array of unit conversion IDs
        }
      }]
    };
    
    const result = await teable.createRecord(userInfo.table_product_id, productPayload);
    const productId = result.records[0].id;
    
    // Reduce credit value after successful creation
    await reduceCreditValue(currentUser);
    
    return res.status(200).json({
      status: 'success',
      detail: 'Tạo sản phẩm thành công',
      product_id: productId,
      product_name: product_name,
      unit_conversions: unit_conversions
    });
    
  } catch (error) {
    return res.status(500).json({
      status: 'error',
      detail: `Lỗi tạo sản phẩm: ${error.message}`
    });
  }
}
```

### 4. Import Slip APIs

#### `/pages/api/import-slips/create-import-slip.js`

```javascript
import { TeableClient } from '../../../lib/teable.js';
import { getCurrentUser } from '../../../lib/auth.js';
import { reduceCreditValue } from '../../../lib/credit.js';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const currentUser = await getCurrentUser(req);
    if (!currentUser) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { supplier_id, import_type, import_slip_details } = req.body;
    const teable = new TeableClient();
    
    // Get user table info
    const userInfo = await getUserTableInfo(currentUser);
    
    // Step 1: Create import slip details
    const detailsPayload = {
      fieldKeyType: 'dbFieldName',
      records: import_slip_details.map(detail => ({
        fields: {
          product_link: [detail.product_id],
          unit_conversions: [detail.unit_conversions_id],
          quantity: detail.quantity,
          unit_price: detail.unit_price,
          vat: detail.vat,
          temp_total: detail.quantity * detail.unit_price,
          final_total: (detail.quantity * detail.unit_price) * (1 + detail.vat / 100)
        }
      }))
    };
    
    const detailsResult = await teable.createRecord(
      userInfo.table_import_slip_detail_id, 
      detailsPayload
    );
    
    // Step 2: Create main import slip
    const importSlipPayload = {
      fieldKeyType: 'dbFieldName',
      records: [{
        fields: {
          supplier_link: [supplier_id],
          import_type: import_type,
          import_slip_details: detailsResult.records.map(r => r.id)
        }
      }]
    };
    
    const result = await teable.createRecord(userInfo.table_import_slip_id, importSlipPayload);
    const importSlipId = result.records[0].id;
    
    // Step 3: Reduce credit value after successful creation
    await reduceCreditValue(currentUser);
    
    // Calculate totals
    const totals = calculateImportTotals(import_slip_details);
    
    return res.status(200).json({
      status: 'success',
      detail: 'Tạo phiếu nhập thành công',
      import_slip_id: importSlipId,
      import_slip_code: `IMP-${importSlipId}`,
      import_slip_details_ids: detailsResult.records.map(r => r.id),
      ...totals
    });
    
  } catch (error) {
    return res.status(500).json({
      status: 'error',
      detail: `Lỗi tạo phiếu nhập: ${error.message}`
    });
  }
}
```

## Critical Implementation Requirements

### 1. **Exact API Compatibility**
- All request/response formats must match FastAPI exactly
- HTTP status codes must be identical
- Error messages must be in Vietnamese
- Field names and data types must match

### 2. **Credit Management Integration**
- Call `reduceCreditValue()` after successful operations in:
  - Order creation
  - Product creation  
  - Product with units creation
  - Import slip creation
- Must be non-blocking (continue even if credit reduction fails)

### 3. **Authentication Flow**
- Implement identical password encoding
- JWT token structure must match
- Token registry system required
- VietQR integration for signup validation

### 4. **Teable Integration**
- Use exact same table structures
- Maintain all field relationships (links, lookups, rollups)
- Preserve auto-generated fields and formulas
- Handle fieldKeyType and viewId parameters correctly

### 5. **Error Handling**
- Comprehensive try-catch blocks
- Detailed error logging
- Vietnamese error messages
- Proper HTTP status codes

### 6. **Data Validation**
- Validate all input data
- Check required fields
- Validate data types and formats
- Handle edge cases gracefully

This guide provides the complete blueprint for building identical functionality in Next.js. Each API must replicate the exact business logic, data flow, and response formats of the original FastAPI system.
