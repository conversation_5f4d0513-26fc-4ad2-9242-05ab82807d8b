#!/usr/bin/env python3
"""
Test script to demonstrate field ID retrieval from Teable API
"""
import requests
import json
import asyncio

# Configuration
TEABLE_BASE_URL = "https://app.teable.io/api"
TEABLE_TOKEN = "Bearer teable_accT1cTLbgDxAw73HQa_xnRuWiEDLat6qqpUDsL4QEzwnKwnkU9ErG7zgJKJswg="

async def get_field_ids_from_table(table_id: str) -> dict:
    """Get field IDs from table using the field API"""
    try:
        headers = {
            "Authorization": TEABLE_TOKEN,
            "Accept": "application/json"
        }
        
        field_info_url = f"{TEABLE_BASE_URL}/table/{table_id}/field"
        field_info_response = requests.get(field_info_url, headers=headers)
        
        print(f"📡 API Call: GET {field_info_url}")
        print(f"📊 Status Code: {field_info_response.status_code}")
        
        if field_info_response.status_code != 200:
            print(f"❌ Could not get field info for {table_id}")
            print(f"Response: {field_info_response.text}")
            return {}
            
        fields = field_info_response.json()
        field_map = {}
        
        print(f"📋 Found {len(fields)} fields:")
        for i, field in enumerate(fields, 1):
            db_field_name = field.get("dbFieldName")
            field_id = field.get("id")
            field_type = field.get("type")
            field_name = field.get("name")
            
            print(f"   {i}. {field_name} ({db_field_name})")
            print(f"      ID: {field_id}")
            print(f"      Type: {field_type}")
            
            if db_field_name and field_id:
                field_map[db_field_name] = field_id
                
        return field_map
        
    except Exception as e:
        print(f"❌ Error getting field IDs from {table_id}: {str(e)}")
        return {}

def test_field_api_structure():
    """Test the field API structure"""
    print("🧪 Testing Field API Structure")
    print("=" * 60)
    
    # Example response structure
    example_response = [
        {
            "id": "fld1234567890abcdef",
            "dbFieldName": "product_name",
            "name": "Tên Sản Phẩm",
            "type": "longText",
            "options": {},
            "description": None,
            "notNull": False,
            "unique": False
        },
        {
            "id": "fld0987654321fedcba",
            "dbFieldName": "unit_price",
            "name": "Đơn Giá",
            "type": "number",
            "options": {
                "precision": 2
            },
            "description": None,
            "notNull": False,
            "unique": False
        },
        {
            "id": "fldabcdef1234567890",
            "dbFieldName": "provisional",
            "name": "Tạm Tính",
            "type": "formula",
            "options": {
                "expression": "{fld0987654321fedcba}*{fld1111222233334444}"
            },
            "description": None,
            "notNull": False,
            "unique": False
        }
    ]
    
    print("📋 Example Field API Response Structure:")
    print("-" * 40)
    print(json.dumps(example_response, indent=2, ensure_ascii=False))
    
    print("\n🔍 Key Information Extracted:")
    print("-" * 40)
    for field in example_response:
        print(f"• {field['name']} ({field['dbFieldName']})")
        print(f"  ID: {field['id']}")
        print(f"  Type: {field['type']}")
        if field['type'] == 'formula':
            print(f"  Expression: {field['options'].get('expression', 'N/A')}")
        print()

async def test_real_table_fields(table_id: str):
    """Test getting fields from a real table"""
    print(f"\n🔍 Testing Real Table: {table_id}")
    print("-" * 60)
    
    field_map = await get_field_ids_from_table(table_id)
    
    if field_map:
        print(f"\n✅ Successfully retrieved {len(field_map)} field mappings:")
        print("-" * 40)
        for db_name, field_id in field_map.items():
            print(f"   {db_name} → {field_id}")
    else:
        print("❌ No field mappings retrieved")

def show_usage_examples():
    """Show how the field IDs are used in different contexts"""
    print("\n📚 Usage Examples:")
    print("=" * 60)
    
    print("🔗 1. Link Field Configuration:")
    print("-" * 40)
    link_example = {
        "type": "link",
        "name": "Sản phẩm",
        "dbFieldName": "product_link",
        "options": {
            "foreignTableId": "tbl1234567890abcdef",
            "relationship": "manyOne"
        }
    }
    print(json.dumps(link_example, indent=2, ensure_ascii=False))
    
    print("\n📊 2. Formula Field Configuration:")
    print("-" * 40)
    formula_example = {
        "type": "formula",
        "options": {
            "expression": "{fld_unit_price_id}*{fld_quantity_id}"
        },
        "dbFieldName": "provisional",
        "name": "Tạm tính"
    }
    print(json.dumps(formula_example, indent=2, ensure_ascii=False))
    
    print("\n📈 3. Rollup Field Configuration:")
    print("-" * 40)
    rollup_example = {
        "type": "rollup",
        "options": {
            "expression": "sum({values})"
        },
        "lookupOptions": {
            "foreignTableId": "tbl_details_table_id",
            "linkFieldId": "fld_link_field_id",
            "lookupFieldId": "fld_provisional_field_id"
        },
        "dbFieldName": "total_provisional",
        "name": "Tổng tạm tính"
    }
    print(json.dumps(rollup_example, indent=2, ensure_ascii=False))

async def main():
    """Main test function"""
    print("🚀 Field ID Retrieval Testing")
    print("=" * 60)
    
    # Show API structure
    test_field_api_structure()
    
    # Test with a real table (you can replace with actual table ID)
    test_table_id = "tblv9Ou1thzbETynKn1"  # Example table ID
    await test_real_table_fields(test_table_id)
    
    # Show usage examples
    show_usage_examples()
    
    print("\n" + "=" * 60)
    print("✅ Field ID Testing Complete!")
    print("\n📝 Key Benefits:")
    print("   • Dynamic field ID retrieval")
    print("   • Proper API-based field mapping")
    print("   • Accurate link/rollup/formula configuration")
    print("   • No hardcoded field IDs")

if __name__ == "__main__":
    asyncio.run(main())
