#!/usr/bin/env python3
"""
Test Enhanced Unit Conversions with Import/Delivery Note Details
"""
import json
import requests
import time

class EnhancedUnitConversionTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.access_token = None
        
    def log_step(self, step_name, description):
        """Log test step"""
        print(f"\n{'='*80}")
        print(f"🧪 STEP: {step_name}")
        print(f"📝 {description}")
        print(f"{'='*80}")
        
    def log_api_call(self, method, endpoint, data=None):
        """Log API call details"""
        print(f"\n📡 API CALL: {method} {endpoint}")
        if data:
            print(f"📋 Request Data: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
    def log_response(self, response):
        """Log API response"""
        print(f"\n📊 RESPONSE:")
        print(f"Status Code: {response.status_code}")
        
        try:
            response_data = response.json()
            print(f"Response Data: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
            
            if response.status_code < 300:
                print("✅ SUCCESS")
            else:
                print("❌ FAILED")
                
            return response_data
            
        except json.JSONDecodeError:
            print(f"Raw Response: {response.text}")
            print("❌ FAILED - Invalid JSON")
            return None
            
    def test_signup(self):
        """Test user signup with enhanced tables"""
        self.log_step("USER_SIGNUP", "Create business account with enhanced unit conversion tables")
        
        signup_data = {
            "username": "**********",
            "password": "cubable2025"
        }
        
        self.log_api_call("POST", "/auth/signup", signup_data)
        
        response = requests.post(f"{self.base_url}/auth/signup", json=signup_data)
        result = self.log_response(response)
        
        if result and response.status_code < 300:
            print(f"🏢 Business: {result.get('business_name', 'N/A')}")
            print(f"📊 Tables Created: {len(result.get('tables', {}))}")
            return True
        return False
        
    def test_signin(self):
        """Test user signin"""
        self.log_step("USER_SIGNIN", "Authenticate and get access token")
        
        signin_data = {
            "username": "**********",
            "password": "cubable2025"
        }
        
        self.log_api_call("POST", "/auth/signin", signin_data)
        
        response = requests.post(f"{self.base_url}/auth/signin", json=signin_data)
        result = self.log_response(response)
        
        if result and response.status_code == 200:
            self.access_token = result.get("accessToken")
            print(f"🔑 Access Token: {self.access_token[:30]}...")
            return True
        return False
        
    def test_create_unit_conversions_with_prices(self):
        """Test creating unit conversions with price information"""
        self.log_step("CREATE_UNIT_CONVERSIONS_WITH_PRICES", "Create beverage unit conversions with pricing")
        
        if not self.access_token:
            print("❌ No access token - skipping test")
            return False
            
        headers = {"Authorization": f"Bearer {self.access_token}"}
        
        # Create unit conversions for beverage products
        unit_conversions = [
            {
                "name_unit": "Chai 330ml",
                "conversion_factor": 1,
                "unit_default": "Chai 330ml",
                "price": 15000
            },
            {
                "name_unit": "Lốc 24 chai",
                "conversion_factor": 24,
                "unit_default": "Chai 330ml",
                "price": 350000
            },
            {
                "name_unit": "Thùng 12 lốc",
                "conversion_factor": 288,
                "unit_default": "Chai 330ml",
                "price": 4200000
            }
        ]
        
        created_units = []
        
        for unit_data in unit_conversions:
            self.log_api_call("POST", "/unit-conversions/create-unit-conversion", unit_data)
            
            response = requests.post(f"{self.base_url}/unit-conversions/create-unit-conversion", json=unit_data, headers=headers)
            result = self.log_response(response)
            
            if result and response.status_code == 200:
                unit_info = result.get('unit_conversion_data', {})
                created_units.append(unit_info)
                print(f"⚖️ Created: {unit_info.get('name_unit')} - {unit_info.get('price', 0):,} VND")
                print(f"   Factor: {unit_info.get('conversion_factor')}, Default: {unit_info.get('unit_default')}")
            
            time.sleep(0.5)  # Brief pause between creations
        
        self.created_unit_conversions = created_units
        return len(created_units) == len(unit_conversions)
        
    def test_create_product_with_enhanced_units(self):
        """Test creating product with enhanced unit conversions"""
        self.log_step("CREATE_PRODUCT_WITH_ENHANCED_UNITS", "Create Coca Cola with pricing units")
        
        if not self.access_token:
            print("❌ No access token - skipping test")
            return False
            
        if not hasattr(self, 'created_unit_conversions'):
            print("❌ No unit conversions available - skipping test")
            return False
            
        # Get unit conversion IDs
        unit_conversion_ids = [unit.get('unit_conversion_id') for unit in self.created_unit_conversions]
        
        product_data = {
            "product_name": "Nước ngọt Coca Cola 330ml",
            "unit_price": 15000,
            "unit_conversions": unit_conversion_ids,
            "vat_rate": 10
        }
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        self.log_api_call("POST", "/products/create-product", product_data)
        
        response = requests.post(f"{self.base_url}/products/create-product", json=product_data, headers=headers)
        result = self.log_response(response)
        
        if result and response.status_code == 200:
            product_info = result.get('product_data', {})
            print(f"🥤 Created Product: {product_info.get('product_name', 'N/A')}")
            print(f"💰 Base Price: {product_info.get('unit_price', 0):,} VND")
            print(f"⚖️ Unit Conversions: {len(product_info.get('unit_conversions', []))}")
            
            self.created_product_id = product_info.get('product_id')
            return True
        return False
        
    def test_enhanced_import_slip(self):
        """Test import slip with unit conversion details"""
        self.log_step("ENHANCED_IMPORT_SLIP", "Create import slip with unit conversion tracking")
        
        if not self.access_token:
            print("❌ No access token - skipping test")
            return False
            
        if not hasattr(self, 'created_product_id'):
            print("❌ No product available - skipping test")
            return False
            
        # Import using different units
        import_data = {
            "import_type": "Nhập mua",
            "import_slip_details": [
                {
                    "product_id": self.created_product_id,
                    "quantity": 10,  # 10 thùng
                    "unit_price": 4200000,  # Price per thùng
                    "vat": 10
                },
                {
                    "product_id": self.created_product_id,
                    "quantity": 5,  # 5 lốc
                    "unit_price": 350000,  # Price per lốc
                    "vat": 10
                }
            ]
        }
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        self.log_api_call("POST", "/create-import-slip", import_data)
        
        response = requests.post(f"{self.base_url}/create-import-slip", json=import_data, headers=headers)
        result = self.log_response(response)
        
        if result and response.status_code == 200:
            print(f"📥 Import Slip: {result.get('import_slip_code', 'N/A')}")
            print(f"💰 Total Amount: {result.get('total_amount', 0):,} VND")
            print(f"📊 Details:")
            print(f"   • 10 Thùng = 2,880 Chai (10 × 288)")
            print(f"   • 5 Lốc = 120 Chai (5 × 24)")
            print(f"   • Total: 3,000 Chai equivalent")
            
            self.created_import_slip = result
            return True
        return False
        
    def test_enhanced_delivery_note(self):
        """Test delivery note with unit conversion tracking"""
        self.log_step("ENHANCED_DELIVERY_NOTE", "Create delivery note with unit conversion calculations")
        
        if not self.access_token:
            print("❌ No access token - skipping test")
            return False
            
        if not hasattr(self, 'created_product_id'):
            print("❌ No product available - skipping test")
            return False
            
        # Deliver using different units
        delivery_data = {
            "order_id": "recOrder001",  # Simulated order ID
            "customer_id": "recCustomer001",  # Simulated customer ID
            "delivery_type": "Xuất bán",
            "delivery_note_details": [
                {
                    "product_id": self.created_product_id,
                    "quantity": 2,  # 2 lốc
                    "unit_price": 350000,  # Price per lốc
                    "vat": 10
                },
                {
                    "product_id": self.created_product_id,
                    "quantity": 24,  # 24 chai lẻ
                    "unit_price": 15000,  # Price per chai
                    "vat": 10
                }
            ]
        }
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        self.log_api_call("POST", "/create-delivery-note", delivery_data)
        
        response = requests.post(f"{self.base_url}/create-delivery-note", json=delivery_data, headers=headers)
        result = self.log_response(response)
        
        if result and response.status_code == 200:
            print(f"📤 Delivery Note: {result.get('delivery_note_code', 'N/A')}")
            print(f"💰 Total Amount: {result.get('total_amount', 0):,} VND")
            print(f"📊 Details:")
            print(f"   • 2 Lốc = 48 Chai (2 × 24)")
            print(f"   • 24 Chai = 24 Chai (24 × 1)")
            print(f"   • Total: 72 Chai equivalent")
            
            return True
        return False
        
    def run_complete_test(self):
        """Run complete enhanced unit conversion test"""
        print("🚀 STARTING ENHANCED UNIT CONVERSION TEST")
        print("=" * 80)
        
        try:
            # Run tests in sequence
            tests = [
                ("Signup", self.test_signup),
                ("Signin", self.test_signin),
                ("Create Unit Conversions with Prices", self.test_create_unit_conversions_with_prices),
                ("Create Product with Enhanced Units", self.test_create_product_with_enhanced_units),
                ("Enhanced Import Slip", self.test_enhanced_import_slip),
                ("Enhanced Delivery Note", self.test_enhanced_delivery_note)
            ]
            
            results = {}
            
            for test_name, test_func in tests:
                success = test_func()
                results[test_name] = success
                
                if not success and test_name in ["Signup", "Signin"]:
                    print(f"❌ Critical test '{test_name}' failed - stopping")
                    break
                    
                time.sleep(1)  # Brief pause between tests
                
            # Generate summary
            self.generate_summary(results)
            
        except Exception as e:
            print(f"❌ Test flow error: {str(e)}")
            
    def generate_summary(self, results):
        """Generate test summary"""
        self.log_step("TEST_SUMMARY", "Enhanced Unit Conversion Test Results")
        
        print("📊 TEST RESULTS:")
        print("-" * 50)
        
        for test_name, success in results.items():
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {test_name}")
            
        passed = sum(1 for success in results.values() if success)
        total = len(results)
        
        print(f"\n📈 Summary: {passed}/{total} tests passed ({(passed/total*100):.1f}%)")
        
        print("\n🎯 ENHANCED FEATURES TESTED:")
        print("-" * 40)
        features = [
            "✅ Unit conversions with price information",
            "✅ Import slip details with unit conversion links",
            "✅ Delivery note details with unit conversion links",
            "✅ Lookup fields for conversion factors",
            "✅ Formula fields for quantity calculations",
            "✅ Multi-unit inventory tracking"
        ]
        
        for feature in features:
            print(f"   {feature}")
            
        print("\n⚖️ BUSINESS BENEFITS:")
        print("-" * 25)
        benefits = [
            "💰 Unit-specific pricing management",
            "📊 Automatic quantity conversions",
            "🔄 Flexible unit switching in transactions",
            "📋 Accurate inventory tracking",
            "🎯 Detailed transaction analysis",
            "⚡ Streamlined multi-unit operations"
        ]
        
        for benefit in benefits:
            print(f"   {benefit}")

if __name__ == "__main__":
    tester = EnhancedUnitConversionTester()
    tester.run_complete_test()
