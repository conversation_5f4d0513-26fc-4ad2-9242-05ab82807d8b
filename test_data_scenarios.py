#!/usr/bin/env python3
"""
Test data scenarios for complete API flow simulation
"""

# Test Business Scenarios
TEST_BUSINESSES = [
    {
        "taxcode": "0316316874",
        "business_name": "CÔNG TY CỔ PHẦN CUBABLE",
        "business_type": "Technology Distribution",
        "password": "cubable2025"
    },
    {
        "taxcode": "0123456789",
        "business_name": "CÔNG TY TNHH THƯƠNG MẠI ABC",
        "business_type": "General Trading",
        "password": "trading123"
    }
]

# Sample Customers
SAMPLE_CUSTOMERS = [
    {
        "phone_number": "0901234567",
        "fullname": "<PERSON><PERSON><PERSON><PERSON>",
        "address": "123 Đường L<PERSON>, Quận 1, TP.HCM",
        "email": "<EMAIL>"
    },
    {
        "phone_number": "0907654321", 
        "fullname": "Trần Thị B<PERSON>nh",
        "address": "456 Đ<PERSON>ờ<PERSON>, Quận 3, TP.HCM",
        "email": "<EMAIL>"
    },
    {
        "phone_number": "0912345678",
        "fullname": "<PERSON><PERSON>",
        "address": "789 Đường V<PERSON> <PERSON>n Tần, Quận 10, TP.HCM", 
        "email": "<EMAIL>"
    },
    {
        "phone_number": "0938765432",
        "fullname": "Phạm Thu Dung",
        "address": "321 Đường Cách Mạng Tháng 8, Quận Tân Bình, TP.HCM",
        "email": "<EMAIL>"
    }
]

# Sample Products - Technology Store
TECH_PRODUCTS = [
    {
        "product_name": "Laptop Dell Inspiron 15 3000",
        "unit_price": 15000000,
        "unit": "Chiếc",
        "vat_rate": 10,
        "category": "Laptop",
        "brand": "Dell"
    },
    {
        "product_name": "Laptop HP Pavilion 14",
        "unit_price": 18000000,
        "unit": "Chiếc",
        "vat_rate": 10,
        "category": "Laptop", 
        "brand": "HP"
    },
    {
        "product_name": "Chuột không dây Logitech M705",
        "unit_price": 500000,
        "unit": "Chiếc",
        "vat_rate": 10,
        "category": "Phụ kiện",
        "brand": "Logitech"
    },
    {
        "product_name": "Bàn phím cơ Keychron K2",
        "unit_price": 2500000,
        "unit": "Chiếc",
        "vat_rate": 10,
        "category": "Phụ kiện",
        "brand": "Keychron"
    },
    {
        "product_name": "Màn hình Samsung 24 inch",
        "unit_price": 3500000,
        "unit": "Chiếc",
        "vat_rate": 10,
        "category": "Màn hình",
        "brand": "Samsung"
    },
    {
        "product_name": "Tai nghe Sony WH-1000XM4",
        "unit_price": 7500000,
        "unit": "Chiếc",
        "vat_rate": 10,
        "category": "Audio",
        "brand": "Sony"
    }
]

# Sample Import Slip Scenarios
IMPORT_SCENARIOS = [
    {
        "scenario_name": "Monthly Tech Inventory Restock",
        "import_type": "Nhập mua",
        "supplier_name": "Công ty TNHH Phân phối Công nghệ Việt Nam",
        "supplier_address": "123 Đường Điện Biên Phủ, Quận Bình Thạnh, TP.HCM",
        "notes": "Nhập hàng đợt 1 tháng 1/2025 - Hàng chính hãng",
        "details": [
            {"product_name": "Laptop Dell Inspiron 15 3000", "quantity": 50, "unit_price": 14000000, "vat": 10},
            {"product_name": "Laptop HP Pavilion 14", "quantity": 30, "unit_price": 17000000, "vat": 10},
            {"product_name": "Chuột không dây Logitech M705", "quantity": 100, "unit_price": 450000, "vat": 10},
            {"product_name": "Bàn phím cơ Keychron K2", "quantity": 40, "unit_price": 2200000, "vat": 10},
            {"product_name": "Màn hình Samsung 24 inch", "quantity": 25, "unit_price": 3200000, "vat": 10}
        ]
    },
    {
        "scenario_name": "Audio Equipment Import",
        "import_type": "Nhập mua",
        "supplier_name": "Công ty CP Thiết bị Âm thanh Chuyên nghiệp",
        "supplier_address": "456 Đường Lê Văn Việt, Quận 9, TP.HCM",
        "notes": "Nhập thiết bị âm thanh cao cấp",
        "details": [
            {"product_name": "Tai nghe Sony WH-1000XM4", "quantity": 20, "unit_price": 7000000, "vat": 10}
        ]
    }
]

# Sample Order Scenarios
ORDER_SCENARIOS = [
    {
        "scenario_name": "Corporate Laptop Order",
        "customer": "Nguyễn Văn An",
        "customer_phone": "0901234567",
        "order_type": "Đơn hàng doanh nghiệp",
        "notes": "Đơn hàng cho văn phòng công ty",
        "details": [
            {"product_name": "Laptop Dell Inspiron 15 3000", "quantity": 5, "unit_price": 15000000, "vat": 10},
            {"product_name": "Chuột không dây Logitech M705", "quantity": 5, "unit_price": 500000, "vat": 10},
            {"product_name": "Bàn phím cơ Keychron K2", "quantity": 5, "unit_price": 2500000, "vat": 10}
        ]
    },
    {
        "scenario_name": "Home Office Setup",
        "customer": "Trần Thị Bình",
        "customer_phone": "0907654321",
        "order_type": "Đơn hàng cá nhân",
        "notes": "Thiết lập văn phòng tại nhà",
        "details": [
            {"product_name": "Laptop HP Pavilion 14", "quantity": 1, "unit_price": 18000000, "vat": 10},
            {"product_name": "Màn hình Samsung 24 inch", "quantity": 1, "unit_price": 3500000, "vat": 10},
            {"product_name": "Tai nghe Sony WH-1000XM4", "quantity": 1, "unit_price": 7500000, "vat": 10}
        ]
    },
    {
        "scenario_name": "Gaming Setup",
        "customer": "Lê Minh Cường",
        "customer_phone": "0912345678", 
        "order_type": "Đơn hàng gaming",
        "notes": "Bộ setup gaming chuyên nghiệp",
        "details": [
            {"product_name": "Laptop Dell Inspiron 15 3000", "quantity": 1, "unit_price": 15000000, "vat": 10},
            {"product_name": "Chuột không dây Logitech M705", "quantity": 1, "unit_price": 500000, "vat": 10},
            {"product_name": "Bàn phím cơ Keychron K2", "quantity": 1, "unit_price": 2500000, "vat": 10},
            {"product_name": "Màn hình Samsung 24 inch", "quantity": 2, "unit_price": 3500000, "vat": 10}
        ]
    }
]

# Voice Transcription Test Cases
VOICE_TRANSCRIPTION_TESTS = [
    {
        "audio_text": "Tôi muốn đặt hai laptop Dell Inspiron và một chuột Logitech",
        "expected_extraction": {
            "products": [
                {"name": "laptop Dell Inspiron", "quantity": 2},
                {"name": "chuột Logitech", "quantity": 1}
            ],
            "customer_info": None
        }
    },
    {
        "audio_text": "Xin chào, tôi là Nguyễn Văn An, số điện thoại 0901234567. Tôi cần đặt một bộ máy tính gồm laptop HP, màn hình Samsung và tai nghe Sony",
        "expected_extraction": {
            "products": [
                {"name": "laptop HP", "quantity": 1},
                {"name": "màn hình Samsung", "quantity": 1},
                {"name": "tai nghe Sony", "quantity": 1}
            ],
            "customer_info": {
                "name": "Nguyễn Văn An",
                "phone": "0901234567"
            }
        }
    },
    {
        "audio_text": "Tôi muốn mua ba bàn phím cơ Keychron và năm chuột không dây cho văn phòng",
        "expected_extraction": {
            "products": [
                {"name": "bàn phím cơ Keychron", "quantity": 3},
                {"name": "chuột không dây", "quantity": 5}
            ],
            "customer_info": None
        }
    }
]

# Invoice Test Scenarios
INVOICE_SCENARIOS = [
    {
        "scenario_name": "Corporate Invoice",
        "buyer_info": {
            "buyerName": "CÔNG TY TNHH CÔNG NGHỆ VIỆT",
            "buyerTaxCode": "0987654321",
            "buyerAddressLine": "123 Đường Cộng Hòa, Quận Tân Bình, TP.HCM",
            "buyerEmail": "<EMAIL>",
            "buyerPhone": "0283456789"
        },
        "payment_method": "Chuyển khoản",
        "invoice_type": "Hóa đơn bán hàng"
    },
    {
        "scenario_name": "Individual Invoice", 
        "buyer_info": {
            "buyerName": "Nguyễn Văn An",
            "buyerTaxCode": "",
            "buyerAddressLine": "123 Đường Lê Lợi, Quận 1, TP.HCM",
            "buyerEmail": "<EMAIL>",
            "buyerPhone": "0901234567"
        },
        "payment_method": "Tiền mặt",
        "invoice_type": "Hóa đơn bán lẻ"
    }
]

# API Test Endpoints
API_ENDPOINTS = {
    "auth": {
        "signup": "/auth/signup",
        "signin": "/auth/signin"
    },
    "inventory": {
        "create_import_slip": "/create-import-slip",
        "create_delivery_note": "/create-delivery-note"
    },
    "orders": {
        "create_order": "/orders/create-order"
    },
    "invoices": {
        "generate_invoice": "/invoices/generate-invoice"
    },
    "transcription": {
        "transcribe": "/transcribe/"
    }
}

# Expected Response Structures
EXPECTED_RESPONSES = {
    "signup": {
        "status": "success",
        "detail": "Tài khoản, không gian, cơ sở dữ liệu và tất cả các bảng đã được tạo thành công",
        "business_name": str,
        "tables": dict,
        "workspace": dict
    },
    "signin": {
        "status": "success", 
        "accessToken": str,
        "detail": "Xác thực thành công",
        "record": list
    },
    "import_slip": {
        "status": "success",
        "detail": "Phiếu nhập đã được tạo thành công",
        "import_slip_id": str,
        "import_slip_code": str,
        "total_amount": float
    },
    "delivery_note": {
        "status": "success",
        "detail": "Phiếu xuất đã được tạo thành công", 
        "delivery_note_id": str,
        "delivery_note_code": str,
        "order_id": str
    },
    "order": {
        "status": "success",
        "order_id": str,
        "order_number": str,
        "total_amount": float
    },
    "invoice": {
        "status": "success",
        "invoice_code": str,
        "pdf_url": str
    }
}

def get_test_scenario(scenario_type, index=0):
    """Get a specific test scenario by type and index"""
    scenarios = {
        "business": TEST_BUSINESSES,
        "customer": SAMPLE_CUSTOMERS,
        "product": TECH_PRODUCTS,
        "import": IMPORT_SCENARIOS,
        "order": ORDER_SCENARIOS,
        "voice": VOICE_TRANSCRIPTION_TESTS,
        "invoice": INVOICE_SCENARIOS
    }
    
    if scenario_type in scenarios:
        data = scenarios[scenario_type]
        return data[index] if index < len(data) else data[0]
    return None

def calculate_order_totals(order_details):
    """Calculate order totals from details"""
    total_temp = sum(item["unit_price"] * item["quantity"] for item in order_details)
    total_vat = sum(item["unit_price"] * item["quantity"] * item["vat"] / 100 for item in order_details)
    total_after_vat = total_temp + total_vat
    
    return {
        "total_temp": total_temp,
        "total_vat": total_vat, 
        "total_after_vat": total_after_vat
    }
