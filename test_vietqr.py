#!/usr/bin/env python3
"""
Test script to verify VietQR API integration
"""
import requests
import json

def test_vietqr_api():
    """Test VietQR API with valid and invalid taxcodes"""
    
    # Test with valid taxcode
    valid_taxcode = "0316316874"  # CUBABLE JSC from the example
    print(f"🧪 Testing VietQR API with valid taxcode: {valid_taxcode}")
    
    try:
        response = requests.get(f"https://api.vietqr.io/v2/business/{valid_taxcode}")
        response.raise_for_status()
        data = response.json()
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        if data.get("code") == "00":
            print("✅ Valid taxcode test passed!")
            print(f"Business Name: {data['data']['name']}")
        else:
            print("❌ Valid taxcode test failed!")
            
    except Exception as e:
        print(f"❌ Error testing valid taxcode: {e}")
    
    print("-" * 50)
    
    # Test with invalid taxcode
    invalid_taxcode = "1234567890"
    print(f"🧪 Testing VietQR API with invalid taxcode: {invalid_taxcode}")
    
    try:
        response = requests.get(f"https://api.vietqr.io/v2/business/{invalid_taxcode}")
        response.raise_for_status()
        data = response.json()
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        if data.get("code") == "51":
            print("✅ Invalid taxcode test passed!")
            print("Expected error response received")
        else:
            print("❌ Invalid taxcode test failed!")
            
    except Exception as e:
        print(f"❌ Error testing invalid taxcode: {e}")

if __name__ == "__main__":
    print("🚀 Testing VietQR API integration...")
    print("-" * 50)
    test_vietqr_api()
    print("-" * 50)
    print("✅ VietQR API tests completed!")
