#!/usr/bin/env python3
"""
Test script to demonstrate dynamic invoice API URLs functionality
"""
import json

def show_dynamic_url_configuration():
    """Show how dynamic URLs work with user configuration"""
    
    print("🔄 Dynamic Invoice API URLs Demo")
    print("=" * 60)
    
    # Simulate user configuration with dynamic URLs
    user_config = {
        "username": "0100109106-507",
        "invoice_type_fieldname": "invoiceType",
        "invoice_code_fieldname": "templateCode", 
        "invoice_series_fieldname": "invoiceSeries",
        "invoice_type": "01GTKT",
        "template_code": "1/772",
        "invoice_series": "C25MMV",
        "invoice_token": "some_base64_token",
        # 🆕 NEW: Dynamic API URLs per user
        "domain_api_invoice": "https://api-vinvoice.viettel.vn/services/einvoiceapplication/api/InvoiceAPI/InvoiceWS/createInvoice",
        "domain_api_get_file": "https://api-vinvoice.viettel.vn/services/einvoiceapplication/api/InvoiceAPI/InvoiceUtilsWS/getInvoiceRepresentationFile"
    }
    
    # Alternative user with different API endpoints
    user_config_alt = {
        "username": "0987654321",
        "invoice_type_fieldname": "invoiceType",
        "invoice_code_fieldname": "templateCode", 
        "invoice_series_fieldname": "invoiceSeries",
        "invoice_type": "02GTKT",
        "template_code": "2/888",
        "invoice_series": "D25MMV",
        "invoice_token": "another_base64_token",
        # 🆕 Different API endpoints for this user
        "domain_api_invoice": "https://custom-api.example.com/invoice/create",
        "domain_api_get_file": "https://custom-api.example.com/invoice/getfile"
    }
    
    def simulate_invoice_generation(config):
        """Simulate the invoice generation process"""
        print(f"\n📋 User: {config['username']}")
        print("-" * 30)
        
        # Extract dynamic URLs
        domain_api_invoice = config.get("domain_api_invoice")
        domain_api_get_file = config.get("domain_api_get_file")
        
        # Extract invoice config
        invoice_type = config.get("invoice_type")
        template_code = config.get("template_code")
        invoice_series = config.get("invoice_series")
        
        print(f"📍 Invoice API URL: {domain_api_invoice}")
        print(f"📍 PDF API URL: {domain_api_get_file}")
        print(f"⚙️ Invoice Type: {invoice_type}")
        print(f"⚙️ Template Code: {template_code}")
        print(f"⚙️ Invoice Series: {invoice_series}")
        
        # Simulate API calls
        create_url = f"{domain_api_invoice}/{config['username']}"
        print(f"\n🔗 Create Invoice Call: POST {create_url}")
        print(f"🔗 Get PDF Call: POST {domain_api_get_file}")
        
        return {
            "create_url": create_url,
            "pdf_url": domain_api_get_file,
            "config_applied": {
                "invoiceType": invoice_type,
                "templateCode": template_code,
                "invoiceSeries": invoice_series
            }
        }
    
    print("📋 OLD Approach (Static URLs):")
    print("-" * 40)
    print("❌ All users use the same hardcoded API endpoints")
    print("❌ No flexibility for different invoice providers")
    print("❌ Configuration changes require code deployment")
    
    print("\n📋 NEW Approach (Dynamic URLs):")
    print("-" * 40)
    print("✅ Each user can have their own API endpoints")
    print("✅ Support for multiple invoice providers")
    print("✅ Configuration changes via database only")
    print("✅ Per-user customization without code changes")
    
    print("\n" + "=" * 60)
    print("🧪 Testing Different User Configurations:")
    
    # Test standard user
    result1 = simulate_invoice_generation(user_config)
    
    # Test alternative user
    result2 = simulate_invoice_generation(user_config_alt)
    
    print("\n" + "=" * 60)
    print("✅ Benefits of Dynamic URLs:")
    print("   • Multi-tenant support for different invoice providers")
    print("   • Per-user API endpoint configuration")
    print("   • Easy switching between test/production environments")
    print("   • No code changes needed for new providers")
    print("   • Centralized configuration in user database")
    
    print("\n📝 Implementation Details:")
    print("   1. URLs stored in user's Teable record:")
    print("      - domain_api_invoice: Invoice creation endpoint")
    print("      - domain_api_get_file: PDF generation endpoint")
    print("   2. Service fetches URLs dynamically per request")
    print("   3. Fallback to static config if user URLs not found")
    print("   4. Full backward compatibility maintained")

if __name__ == "__main__":
    show_dynamic_url_configuration()
