# Plan Status API Documentation

## Overview

This API provides functionality to retrieve plan status information from <PERSON><PERSON> using a plan status ID. The API calls the Teable service directly and returns all plan data fields in a structured format.

## API Endpoint

**POST** `/plan-status/get-status-plan`

## Request

### Request Schema
```json
{
  "plan_status_id": "string"
}
```

### Example Request
```json
{
  "plan_status_id": "recdDix5FO3DxcvYIoD"
}
```

## Response

### Success Response (200)
```json
{
  "status": "success",
  "message": "L<PERSON>y thông tin plan status thành công",
  "data": {
    "fields": {
      "Nhan": 1,
      "So": {
        "id": "rec5e5txg7k5USs1CZw",
        "title": "4"
      },
      "started_time": "2025-07-15T08:44:38.952Z",
      "cycle": 12,
      "time_expired": "2026-07-15T08:44:38.952Z",
      "Ngay": "2025-07-15T09:04:00.000Z",
      "status": "Đang hoạt động",
      "credit_value": 4000,
      "Tai_khoan": {
        "id": "recSzqmkuTChxV3rOiu",
        "title": "25031989"
      },
      "name_plan": "Cơ bản"
    },
    "name": "1",
    "id": "recdDix5FO3DxcvYIoD",
    "autoNumber": 1,
    "createdTime": "2025-07-15T08:44:38.952Z",
    "lastModifiedTime": "2025-07-17T09:05:06.690Z",
    "createdBy": "usr6cQql0CGD5qqSuPX",
    "lastModifiedBy": "usr6cQql0CGD5qqSuPX"
  }
}
```

### Error Responses

#### 404 Not Found
```json
{
  "detail": "Không tìm thấy plan status với ID: {plan_status_id}"
}
```

#### 400 Bad Request
```json
{
  "detail": "Lỗi khi gọi API Teable: {status_code}"
}
```

#### 500 Internal Server Error
```json
{
  "detail": "Lỗi máy chủ không mong muốn: {error_message}"
}
```

## Field Descriptions

| Field | Type | Description |
|-------|------|-------------|
| `Nhan` | integer | Plan identifier number |
| `So` | object | Contains id and title information |
| `started_time` | datetime | Plan start time (ISO format) |
| `cycle` | integer | Plan cycle duration |
| `time_expired` | datetime | Plan expiration time (ISO format) |
| `Ngay` | datetime | Plan date (ISO format) |
| `status` | string | Plan status (e.g., "Đang hoạt động") |
| `credit_value` | integer | Credit value of the plan |
| `Tai_khoan` | object | Account information with id and title |
| `name_plan` | string | Plan name (e.g., "Cơ bản") |

## Usage Examples

### cURL
```bash
curl -X POST "http://localhost:8001/plan-status/get-status-plan" \
  -H "Content-Type: application/json" \
  -d '{
    "plan_status_id": "recdDix5FO3DxcvYIoD"
  }'
```

### Python
```python
import requests

url = "http://localhost:8001/plan-status/get-status-plan"
data = {
    "plan_status_id": "recdDix5FO3DxcvYIoD"
}

response = requests.post(url, json=data)
plan_data = response.json()

if response.status_code == 200:
    fields = plan_data['data']['fields']
    print(f"Plan Status: {fields['status']}")
    print(f"Plan Name: {fields['name_plan']}")
    print(f"Credit Value: {fields['credit_value']}")
    print(f"Account: {fields['Tai_khoan']['title']}")
else:
    print(f"Error: {plan_data['detail']}")
```

### JavaScript
```javascript
const response = await fetch('http://localhost:8001/plan-status/get-status-plan', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    plan_status_id: 'recdDix5FO3DxcvYIoD'
  })
});

const planData = await response.json();

if (response.ok) {
  const fields = planData.data.fields;
  console.log('Plan Status:', fields.status);
  console.log('Plan Name:', fields.name_plan);
  console.log('Credit Value:', fields.credit_value);
} else {
  console.error('Error:', planData.detail);
}
```

## Technical Implementation

### Files Created
- `app/schemas/plan_status.py` - Pydantic schemas for request/response
- `app/services/plan_status_service.py` - Business logic and Teable API integration
- `app/routes/plan_status.py` - FastAPI route definition

### Key Features
- ✅ Direct integration with Teable API
- ✅ Uses Bearer token from .env configuration
- ✅ Proper error handling for all scenarios
- ✅ Structured response with typed data
- ✅ DateTime parsing for time fields
- ✅ Nested object parsing for complex fields
- ✅ Comprehensive logging for debugging

### Configuration
The API uses the following configuration from `.env`:
- `TEABLE_BASE_URL` - Base URL for Teable API
- `TEABLE_TOKEN` - Bearer token for authentication

### Teable API Details
- **Table ID**: `tblL2pLkyLQgPzmCVHU`
- **Parameter**: `fieldKeyType=dbFieldName`
- **Method**: GET request to retrieve record by ID
- **Authentication**: Bearer token from environment

## Error Handling

The API includes comprehensive error handling for:
- Invalid plan status IDs (404 Not Found)
- Teable API errors (400 Bad Request)
- Network/connection issues (500 Internal Server Error)
- Invalid request format (422 Validation Error)

## Security

- Uses Bearer token authentication with Teable
- Input validation through Pydantic schemas
- Proper error message sanitization
- Request/response logging for audit trails

## Performance

- Direct API calls to Teable without caching
- Efficient datetime parsing
- Minimal data transformation overhead
- Structured response for easy client consumption
