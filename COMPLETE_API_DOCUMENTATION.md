# Order Voice Backend - Complete API Documentation

## Application Overview

**Order Voice Backend** is a FastAPI-based REST API system for managing orders, products, customers, suppliers, and inventory operations. The system integrates with Teable (a database service) for data storage and includes features like voice transcription, authentication, invoice generation, and credit management.

### Technology Stack
- **Backend Framework**: FastAPI (Python)
- **Database**: Teable API integration
- **Authentication**: JWT tokens + custom encoding
- **External APIs**: VietQR API, Voice transcription services
- **Deployment**: Docker on port 8001

## Environment Configuration (.env)

```env
# Teable API Configuration
TEABLE_BASE_URL=https://app.teable.vn/api
TEABLE_TOKEN=Bearer teable_acc...
TEABLE_TABLE_ID=tblj52nsIFcIWDAW4fr
TEABLE_USER_VIEW_ID=viwWOH429ek2bW3eU06
TEABLE_TOKEN_LIST_TABLE_ID=tblR7dckuSizsZlhW47

# Application Configuration
JWT_SECRET_KEY=CUBABLE_JWT_SECRET_2025
VIETQR_API_BASE_URL=https://api.vietqr.io/v2/business

# Plan Status Configuration
PLAN_STATUS_TABLE_ID=tblL2pLkyLQgPzmCVHU
```

## Core Features

### 1. Credit Management System
- Users have credit_value in their plan status
- Each successful operation (order, product, import slip) reduces credit by 1
- Credit is managed through plan_status table with automatic deduction

### 2. Authentication System
- Custom password encoding with private rules
- JWT token generation and management
- Token registry for user session tracking
- VietQR integration for business validation

### 3. Teable Integration
- All data stored in Teable tables
- Dynamic table creation during signup
- Lookup fields for data relationships
- Real-time inventory tracking

## API Routes Structure

The application consists of 10 main route modules:

## 1. TRANSCRIPTION API (`/transcription`)

### Purpose
Voice-to-text transcription services for order processing.

### Endpoints
- `POST /transcription/process` - Process voice input to text
- `POST /transcription/analyze` - Analyze transcribed text for order data

### Key Functions
- Voice file upload and processing
- Speech-to-text conversion
- Order data extraction from voice commands
- Multi-language support (Vietnamese focus)

## 2. AUTHENTICATION API (`/auth`)

### Purpose
User authentication, registration, and session management.

### Endpoints

#### `POST /signin`
**Request:**
```json
{
  "username": "string",
  "password": "string"
}
```
**Response:**
```json
{
  "status": "success",
  "accessToken": "jwt_token",
  "detail": "message",
  "record": [user_data]
}
```

#### `POST /signup`
**Request:**
```json
{
  "username": "taxcode",
  "password": "string"
}
```
**Response:**
```json
{
  "status": "success",
  "detail": "message",
  "account_id": "string",
  "business_name": "string",
  "workspace": {
    "space_id": "string",
    "base_id": "string",
    "access_token": "string"
  },
  "tables": {
    "order_table_id": "string",
    "product_table_id": "string",
    // ... all table IDs
  }
}
```

### Key Functions
- Password encoding with private rules
- VietQR API integration for business validation
- Automatic workspace creation (space, base, tables)
- JWT token generation and storage
- User profile management

### Authentication Flow
1. Validate taxcode via VietQR API
2. Create user account with encoded password
3. Generate Teable workspace (space + base)
4. Create all required tables with relationships
5. Generate access token and store in registry
6. Return complete workspace information

## 3. ORDERS API (`/orders`)

### Purpose
Order creation and management with automatic delivery note generation.

### Endpoints

#### `POST /orders/create-order`
**Request:**
```json
{
  "customer_id": "string",
  "order_details": [
    {
      "product_id": "string",
      "unit_conversions_id": "string",
      "unit_price": "number",
      "quantity": "number",
      "vat": "number"
    }
  ],
  "delivery_type": "string"
}
```
**Response:**
```json
{
  "status": "success",
  "detail": "message",
  "order_id": "string",
  "order_code": "string",
  "delivery_note_id": "string",
  "delivery_note_code": "string",
  "customer_id": "string",
  "total_items": "number",
  "total_temp": "number",
  "total_vat": "number",
  "total_after_vat": "number"
}
```

### Key Functions
- Order detail creation with calculations
- Main order record creation
- Automatic delivery note generation
- **Credit reduction by 1** after successful completion
- VAT and total calculations
- Customer and product linking

### Order Flow
1. Get user table information
2. Create order details with calculations
3. Create main order record
4. Auto-generate delivery note
5. Reduce user credit by 1
6. Return complete order information

## 4. INVOICES API (`/invoices`)

### Purpose
Invoice generation and management for orders.

### Endpoints
- `POST /invoices/generate` - Generate invoice from order
- `GET /invoices/{invoice_id}` - Get invoice details
- `POST /invoices/send` - Send invoice to customer

### Key Functions
- Invoice template processing
- PDF generation
- Invoice numbering system
- Email delivery integration
- Invoice status tracking

## 5. IMPORT SLIPS API (`/import-slips`)

### Purpose
Inventory import management with supplier integration.

### Endpoints

#### `POST /import-slips/create-import-slip`
**Request:**
```json
{
  "supplier_id": "string",
  "import_type": "string",
  "import_slip_details": [
    {
      "product_id": "string",
      "unit_conversions_id": "string",
      "quantity": "number",
      "unit_price": "number",
      "vat": "number"
    }
  ]
}
```
**Response:**
```json
{
  "status": "success",
  "detail": "message",
  "import_slip_id": "string",
  "import_slip_code": "string",
  "import_slip_details_ids": ["string"],
  "total_items": "number",
  "total_amount": "number"
}
```

#### `POST /import-slips/create-delivery-note`
**Request:**
```json
{
  "order_id": "string",
  "customer_id": "string",
  "delivery_type": "string",
  "delivery_note_details": [
    {
      "product_id": "string",
      "unit_conversions_id": "string",
      "quantity": "number"
    }
  ]
}
```

### Key Functions
- Import slip creation with details
- Supplier linking and validation
- **Credit reduction by 1** after successful completion
- Inventory quantity updates
- Cost calculations with VAT
- Delivery note generation

## 6. CUSTOMERS API (`/customers`)

### Purpose
Customer relationship management.

### Endpoints

#### `POST /customers/create-customer`
**Request:**
```json
{
  "phone_number": "string",
  "fullname": "string",
  "address": "string",
  "email": "string"
}
```

#### `POST /customers/find-customers`
**Request:**
```json
{
  "search_term": "string"
}
```

### Key Functions
- Customer profile creation
- Customer search by name/phone
- Address and contact management
- Customer history tracking

## 7. PRODUCTS API (`/products`)

### Purpose
Product catalog management with unit conversions.

### Endpoints

#### `POST /products/create-product`
**Request:**
```json
{
  "product_name": "string",
  "unit_conversions": ["unit_conversion_id"]
}
```

#### `POST /products/create-product-with-units`
**Request:**
```json
{
  "product_name": "string",
  "unit_conversions": [
    {
      "name_unit": "string",
      "conversion_factor": "number",
      "unit_default": "string",
      "price": "number",
      "vat": "number"
    }
  ]
}
```

#### `POST /products/find-products`
**Request:**
```json
{
  "search_term": "string"
}
```

### Key Functions
- Product creation with unit conversions
- **Credit reduction by 1** after successful creation
- Product search functionality
- Unit conversion management
- Price and VAT configuration
- Inventory tracking integration

## 8. UNIT CONVERSIONS API (`/unit-conversions`)

### Purpose
Unit of measurement management for products.

### Endpoints

#### `POST /unit-conversions/create-unit-conversion`
**Request:**
```json
{
  "name_unit": "string",
  "conversion_factor": "number",
  "unit_default": "string",
  "price": "number",
  "vat": "number"
}
```

#### `GET /unit-conversions/list`
**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "id": "string",
      "name_unit": "string",
      "conversion_factor": "number",
      "unit_default": "string",
      "price": "number",
      "vat": "number"
    }
  ]
}
```

### Key Functions
- Unit conversion creation
- Conversion factor calculations
- Price per unit management
- VAT rate configuration
- Default unit designation

## 9. SUPPLIERS API (`/suppliers`)

### Purpose
Supplier relationship management for imports.

### Endpoints

#### `POST /suppliers/create-supplier`
**Request:**
```json
{
  "supplier_name": "string",
  "address": "string",
  "phone": "string",
  "email": "string"
}
```

#### `POST /suppliers/find-suppliers`
**Request:**
```json
{
  "search_term": "string"
}
```

### Key Functions
- Supplier profile creation
- Supplier search functionality
- Contact information management
- Import history tracking

## 10. PLAN STATUS API (`/plan-status`)

### Purpose
User plan and credit management.

### Endpoints

#### `POST /plan-status/get-status-plan`
**Request:**
```json
{
  "plan_status_id": "string"
}
```
**Response:**
```json
{
  "status": "success",
  "message": "string",
  "data": {
    "fields": {
      "Nhan": "number",
      "So": {"id": "string", "title": "string"},
      "started_time": "datetime",
      "cycle": "number",
      "time_expired": "datetime",
      "Ngay": "datetime",
      "status": "string",
      "credit_value": "number",
      "Tai_khoan": {"id": "string", "title": "string"},
      "name_plan": "string"
    },
    "name": "string",
    "id": "string",
    "autoNumber": "number",
    "createdTime": "datetime",
    "lastModifiedTime": "datetime",
    "createdBy": "string",
    "lastModifiedBy": "string"
  }
}
```

### Key Functions
- Plan status retrieval
- Credit balance checking
- Plan expiration management
- Usage tracking

## Database Schema (Teable Tables)

### User Table
- `username` (string, unique) - User identifier/taxcode
- `password` (string) - Encoded password
- `business_name` (string) - Company name from VietQR
- `email` (string) - User email
- `phone` (string) - User phone
- `role` (string) - User role
- `current_plan` (link) - Link to plan status table
- `space_token` (string) - Teable workspace token
- `table_*_id` (string) - Various table IDs for user workspace
- `last_login` (datetime) - Last login timestamp

### Plan Status Table (tblL2pLkyLQgPzmCVHU)
- `Nhan` (number) - Plan identifier
- `So` (link) - Reference number
- `started_time` (datetime) - Plan start time
- `cycle` (number) - Plan cycle duration
- `time_expired` (datetime) - Plan expiration
- `Ngay` (datetime) - Plan date
- `status` (string) - Plan status ("Đang hoạt động")
- `credit_value` (number) - Available credits
- `Tai_khoan` (link) - Account reference
- `name_plan` (string) - Plan name ("Cơ bản")

### Customer Table
- `phone_number` (string, unique) - Customer phone
- `fullname` (string) - Customer full name
- `address` (longText) - Customer address
- `email` (string) - Customer email

### Product Table
- `product_name` (string) - Product name
- `unit_conversions` (link, manyMany) - Link to unit conversions
- `total_imported` (rollup) - Total imported quantity
- `total_exported` (rollup) - Total exported quantity
- `inventory` (formula) - Current stock (imported - exported)

### Unit Conversions Table
- `name_unit` (string) - Unit name
- `conversion_factor` (number) - Conversion factor
- `unit_default` (string) - Default unit
- `price` (number) - Unit price
- `vat_rate` (number) - VAT percentage

### Order Table
- `order_number` (formula) - Auto-generated order number
- `customer_link` (link) - Link to customer
- `customer_name` (lookup) - Customer fullname lookup
- `customer_phone` (lookup) - Customer phone lookup
- `invoice_details` (link) - Link to order details
- `invoice_file` (attachment) - Order file upload

### Order Detail Table
- `number_order_detail` (autoNumber) - Detail number
- `product_link` (link) - Link to product
- `product_name_lookup` (lookup) - Product name lookup
- `unit_conversions` (link) - Link to unit conversions
- `unit_price` (number) - Unit price
- `quantity` (number) - Quantity
- `vat` (number) - VAT percentage
- `temp_total` (number) - Subtotal
- `final_total` (number) - Total with VAT

### Import Slip Table
- `import_slip_code` (formula) - Auto-generated code
- `import_slip_details` (link) - Link to import details
- `supplier_link` (link) - Link to supplier
- `supplier_name_lookup` (lookup) - Supplier name lookup
- `import_type` (string) - Import type

### Import Slip Details Table
- `number_detail` (autoNumber) - Detail number
- `product_link` (link) - Link to product
- `unit_conversions` (link) - Link to unit conversions
- `quantity` (number) - Quantity
- `unit_price` (number) - Unit price
- `vat` (number) - VAT percentage
- `temp_total` (number) - Subtotal
- `final_total` (number) - Total with VAT
- `quantity_unit_default` (formula) - Converted quantity

### Delivery Note Table
- `delivery_note_code` (formula) - Auto-generated code
- `customer_link` (link) - Link to customer
- `customer_name` (lookup) - Customer name lookup
- `order_link` (link) - Link to order
- `delivery_note_details` (link) - Link to delivery details
- `delivery_type` (string) - Delivery type

### Supplier Table
- `supplier_number` (autoNumber) - Supplier number
- `supplier_name` (longText) - Supplier name
- `address` (longText) - Supplier address

## Credit Management System

### Credit Reduction Triggers
All these operations reduce credit_value by 1:
1. **Order Creation** - After successful order + delivery note
2. **Product Creation** - After successful product creation
3. **Product with Units Creation** - After successful creation
4. **Import Slip Creation** - After successful import slip

### Credit Reduction Process
1. Get user's `current_plan` ID from user table
2. Retrieve current `credit_value` from plan status table
3. Reduce `credit_value` by 1 using PATCH API
4. Update plan status record
5. Log operation for audit trail

### Credit Reduction Function
```python
async def reduce_credit_value_on_order_complete(username: str) -> bool:
    # Implementation details in plan_status_service.py
    # Returns True if successful, False if failed
    # Non-blocking - main operation continues regardless
```

## Authentication & Security

### Password Encoding
Custom encoding with private rules:
1. Combine password with username as salt
2. Apply HMAC-SHA256 with secret key
3. Base64 encode result
4. Add custom prefix/suffix

### JWT Token Structure
```json
{
  "username": "string",
  "role": "string",
  "permissions": ["array"],
  "exp": "expiration_timestamp",
  "iat": "issued_at_timestamp"
}
```

### Token Management
- Tokens stored in token registry table
- Automatic token refresh
- Session tracking and management

## API Integration Patterns

### Teable API Calls
All data operations use Teable REST API:
- **GET** `/table/{table_id}/record` - List records
- **POST** `/table/{table_id}/record` - Create records
- **PATCH** `/table/{table_id}/record/{record_id}` - Update record
- **DELETE** `/table/{table_id}/record/{record_id}` - Delete record

### Common Parameters
- `fieldKeyType: "dbFieldName"` - Use database field names
- `viewId: "{view_id}"` - Use specific view
- `typecast: true` - Auto-convert data types

### Error Handling
- Comprehensive HTTP status code handling
- Detailed error messages in Vietnamese
- Logging for all operations
- Graceful degradation for non-critical failures

## Lookup Fields System

### Customer Lookups
- Order table → Customer fullname, phone
- Delivery note table → Customer fullname, phone

### Product Lookups
- Order detail table → Product name

### Supplier Lookups
- Import slip table → Supplier name, address

### Implementation
Lookup fields automatically created during signup:
1. Get field IDs from source tables
2. Create lookup fields with proper configuration
3. Link to foreign tables with correct relationships

## File Upload System

### Order File Uploads
- File attachment field in order table
- Upload file ID stored in user table
- Support for multiple file formats

## Inventory Tracking

### Automatic Calculations
- **Total Imported**: Rollup sum from import slip details
- **Total Exported**: Rollup sum from delivery note details
- **Current Inventory**: Formula (imported - exported)

### Real-time Updates
- Inventory updated on each import/export operation
- Automatic quantity conversions using unit factors
- Stock level monitoring and alerts

## Next.js Implementation Guidelines

### API Client Setup
```typescript
// Base API configuration
const API_BASE_URL = 'http://localhost:8001'
const TEABLE_BASE_URL = 'https://app.teable.vn/api'

// Headers for all requests
const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${token}`
}
```

### State Management
- Use React Query/SWR for API state management
- Implement optimistic updates for better UX
- Cache frequently accessed data (customers, products)

### Authentication Flow
1. Login form → POST /auth/signin
2. Store JWT token in secure storage
3. Add token to all subsequent requests
4. Implement token refresh mechanism
5. Handle authentication errors gracefully

### Form Handling
- Use React Hook Form for complex forms
- Implement real-time validation
- Handle file uploads for order attachments
- Auto-save drafts for long forms

### Data Relationships
- Implement cascading dropdowns (customer → orders)
- Auto-populate related fields using lookups
- Handle many-to-many relationships (products ↔ units)

### Credit Management UI
- Display current credit balance
- Show credit deduction notifications
- Implement credit purchase flow
- Alert users when credit is low

### Error Handling
- Global error boundary for unhandled errors
- Toast notifications for user feedback
- Retry mechanisms for failed requests
- Offline support with queue sync

### Performance Optimization
- Implement pagination for large datasets
- Use virtual scrolling for long lists
- Lazy load components and routes
- Optimize bundle size with code splitting

This documentation provides complete information for rewriting the FastAPI backend as a Next.js application with proper API integration, state management, and all the features of the original system.
