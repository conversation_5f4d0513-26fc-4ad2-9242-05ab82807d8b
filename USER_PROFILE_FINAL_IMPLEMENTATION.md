# User Profile API - Final Implementation

## Overview

The user profile APIs have been implemented to use Teable access tokens directly from the Authorization header to query and update user information in the user table.

## Implementation Details

### Authentication Method
- **Token Source**: Authorization header with Bearer token
- **Token Type**: Teable access_token (not JWT)
- **Query Method**: Direct search in user table using `access_token` field
- **Authorization**: Uses `TEABLE_TOKEN` from environment for API calls

### API Endpoints

#### GET /user/me
**Purpose**: Get current user profile information

**Request:**
```http
GET /user/me HTTP/1.1
Authorization: Bearer teable_accWhxU2ZkU3O9brgx8_hprrcGxBozswLxEaJSW6J5MGhQ3BmuGf92NG3xGkON0=
```

**Response:**
```json
{
  "status": "success",
  "message": "L<PERSON>y thông tin người dùng thành công",
  "data": {
    "username": "27102001",
    "business_name": "<PERSON>ông ty C<PERSON> phần CUBABLE",
    "current_plan_name": "Nâng cao",
    "last_login": "2025-07-08T03:28:23.478Z"
  }
}
```

#### PATCH /user/update-profile
**Purpose**: Update user profile information

**Request:**
```http
PATCH /user/update-profile HTTP/1.1
Authorization: Bearer teable_accWhxU2ZkU3O9brgx8_hprrcGxBozswLxEaJSW6J5MGhQ3BmuGf92NG3xGkON0=
Content-Type: application/json

{
  "business_name": "Công ty Cổ phần CUBABLE Updated"
}
```

**Response:**
```json
{
  "status": "success",
  "message": "Cập nhật thông tin thành công",
  "data": {
    "username": "27102001",
    "business_name": "Công ty Cổ phần CUBABLE Updated",
    "current_plan_name": "Nâng cao",
    "last_login": "2025-07-08T03:28:23.478Z"
  }
}
```

## Technical Implementation

### Teable Query Parameters
```json
{
  "fieldKeyType": "dbFieldName",
  "viewId": "viw6ye3dhnsRIJXAV4p",
  "filter": {
    "conjunction": "and",
    "filterSet": [
      {
        "fieldId": "access_token",
        "operator": "is",
        "value": "teable_access_token_from_header"
      }
    ]
  }
}
```

### Update Payload
```json
{
  "fieldKeyType": "dbFieldName",
  "typecast": true,
  "record": {
    "fields": {
      "business_name": "Updated Business Name"
    }
  }
}
```

### Authentication Flow
1. Extract Teable access_token from `Authorization: Bearer {token}` header
2. Validate Bearer token format
3. Query user table using access_token filter with viewId `viw6ye3dhnsRIJXAV4p`
4. Return user profile or perform update operation
5. Handle authentication errors gracefully

### Files Created
- `app/schemas/user_profile.py` - Pydantic schemas for request/response
- `app/services/user_profile_service.py` - Business logic and Teable integration
- `app/routes/user_profile.py` - FastAPI route definitions
- Updated `app/main.py` - Router registration

### Key Features
- ✅ Uses Teable access_token directly from Authorization header
- ✅ Queries user table by access_token with specific viewId
- ✅ Only updates fields that have non-null values
- ✅ Returns user profile after successful updates
- ✅ Comprehensive error handling with Vietnamese messages
- ✅ Proper HTTP status codes for all scenarios

### Editable Fields
Currently, only the following field can be updated:
- `business_name` (string) - Company/business name

### Error Handling
- **401 Unauthorized**: Missing, invalid, or empty Authorization header
- **404 Not Found**: User not found with provided access_token
- **400 Bad Request**: Teable API errors
- **500 Internal Server Error**: Unexpected server errors

## Usage Examples

### cURL Examples

#### Get User Profile
```bash
curl -X GET "http://localhost:8001/user/me" \
  -H "Authorization: Bearer teable_accWhxU2ZkU3O9brgx8_hprrcGxBozswLxEaJSW6J5MGhQ3BmuGf92NG3xGkON0="
```

#### Update Profile
```bash
curl -X PATCH "http://localhost:8001/user/update-profile" \
  -H "Authorization: Bearer teable_accWhxU2ZkU3O9brgx8_hprrcGxBozswLxEaJSW6J5MGhQ3BmuGf92NG3xGkON0=" \
  -H "Content-Type: application/json" \
  -d '{
    "business_name": "Công ty Cổ phần CUBABLE Updated"
  }'
```

### JavaScript Examples

#### Get User Profile
```javascript
const response = await fetch('http://localhost:8001/user/me', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer teable_accWhxU2ZkU3O9brgx8_hprrcGxBozswLxEaJSW6J5MGhQ3BmuGf92NG3xGkON0='
  }
});

const userProfile = await response.json();
console.log(userProfile.data.business_name);
```

#### Update Profile
```javascript
const response = await fetch('http://localhost:8001/user/update-profile', {
  method: 'PATCH',
  headers: {
    'Authorization': 'Bearer teable_accWhxU2ZkU3O9brgx8_hprrcGxBozswLxEaJSW6J5MGhQ3BmuGf92NG3xGkON0=',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    business_name: 'Công ty Cổ phần CUBABLE Updated'
  })
});

const updatedProfile = await response.json();
console.log(updatedProfile.data.business_name);
```

## Configuration Requirements

### Environment Variables
- `TEABLE_BASE_URL` - Base URL for Teable API
- `TEABLE_TOKEN` - Bearer token for Teable authentication
- `TEABLE_TABLE_ID` - User table ID in Teable

### Constants
- `USER_PROFILE_VIEW_ID = "viw6ye3dhnsRIJXAV4p"` - View ID for user profile queries

## Benefits

- ✅ **Direct Teable Integration**: Uses Teable access tokens directly
- ✅ **Simple Authentication**: No complex JWT validation needed
- ✅ **Efficient Queries**: Direct access_token lookup in user table
- ✅ **Smart Updates**: Only non-null fields included in updates
- ✅ **Error Resilience**: Comprehensive error handling
- ✅ **Vietnamese Messages**: User-friendly error messages

## Notes

- The token in Authorization header is the Teable access_token stored in user table
- Uses viewId `viw6ye3dhnsRIJXAV4p` for all user table queries
- Currently only `business_name` field is editable
- Update payload automatically excludes null/empty fields
- Returns updated profile immediately after successful changes
