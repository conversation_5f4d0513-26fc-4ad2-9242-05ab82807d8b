#!/usr/bin/env python3
"""
Test script to verify invoice configuration logic
"""
import json

def test_invoice_config_injection():
    """Test the invoice configuration injection logic"""
    
    print("🧪 Testing Invoice Configuration Injection")
    print("=" * 50)
    
    # Simulate user configuration from Teable
    user_config = {
        "username": "0100109106-507",
        "invoice_type_fieldname": "invoiceType",
        "invoice_code_fieldname": "templateCode", 
        "invoice_series_fieldname": "invoiceSeries",
        "invoice_type": "01GTKT",
        "template_code": "1/772",
        "invoice_series": "C25MMV",
        "invoice_token": "some_base64_token"
    }
    
    # Input payload (without config fields)
    input_payload = {
        "generalInvoiceInfo": {
            "currencyCode": "VND",
            "adjustmentType": "1",
            "paymentStatus": True,
            "cusGetInvoiceRight": True
        },
        "buyerInfo": {
            "buyerName": "Trần Thế Anh"
        },
        "itemInfo": [
            {
                "lineNumber": 1,
                "itemName": "Hàng hóa 01",
                "unitName": "Chiếc",
                "unitPrice": 150450,
                "quantity": 10
            }
        ]
    }
    
    # Simulate the injection logic from the service
    def inject_invoice_config(payload, config):
        """Simulate the config injection logic"""
        result_payload = payload.copy()
        
        # Ensure generalInvoiceInfo exists
        if "generalInvoiceInfo" not in result_payload:
            result_payload["generalInvoiceInfo"] = {}
        
        # Get field names and values
        invoice_type_fieldname = config.get("invoice_type_fieldname", "invoiceType")
        invoice_code_fieldname = config.get("invoice_code_fieldname", "templateCode")
        invoice_series_fieldname = config.get("invoice_series_fieldname", "invoiceSeries")
        
        invoice_type = config.get("invoice_type")
        template_code = config.get("template_code")
        invoice_series = config.get("invoice_series")
        
        # Inject configuration values
        result_payload["generalInvoiceInfo"][invoice_type_fieldname] = invoice_type
        result_payload["generalInvoiceInfo"][invoice_code_fieldname] = template_code
        result_payload["generalInvoiceInfo"][invoice_series_fieldname] = invoice_series
        
        return result_payload
    
    # Test the injection
    final_payload = inject_invoice_config(input_payload, user_config)
    
    print("📋 User Configuration:")
    print("-" * 30)
    for key, value in user_config.items():
        if key.startswith("invoice"):
            print(f"   {key}: {value}")
    
    print("\n📋 Input Payload (Before Injection):")
    print("-" * 30)
    print(json.dumps(input_payload, indent=2, ensure_ascii=False))
    
    print("\n📋 Final Payload (After Injection):")
    print("-" * 30)
    print(json.dumps(final_payload, indent=2, ensure_ascii=False))
    
    print("\n✅ Verification:")
    general_info = final_payload["generalInvoiceInfo"]
    print(f"   • invoiceType: {general_info.get('invoiceType')} ✓")
    print(f"   • templateCode: {general_info.get('templateCode')} ✓")
    print(f"   • invoiceSeries: {general_info.get('invoiceSeries')} ✓")
    print(f"   • Original fields preserved: {len(input_payload['generalInvoiceInfo'])} → {len(general_info)} fields")

if __name__ == "__main__":
    test_invoice_config_injection()
