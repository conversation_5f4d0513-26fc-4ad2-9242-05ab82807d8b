# User Profile API Documentation

## Overview

This documentation covers the new user profile management APIs that allow users to retrieve and update their profile information using Teable integration with access_token authentication.

## APIs Created

### 1. GET /user/me
**Purpose**: Get current user profile information  
**Authentication**: Required - Bearer token in Authorization header

### 2. PATCH /user/update-profile  
**Purpose**: Update user profile information  
**Authentication**: Required - Bearer token in Authorization header

## API Specifications

### GET /user/me

#### Request
```http
GET /user/me HTTP/1.1
Authorization: Bearer teable_accWhxU2ZkU3O9brgx8_hprrcGxBozswLxEaJSW6J5MGhQ3BmuGf92NG3xGkON0=
```

#### Response (200 OK)
```json
{
  "status": "success",
  "message": "L<PERSON>y thông tin người dùng thành công",
  "data": {
    "username": "27102001",
    "business_name": "Công ty Cổ phần CUBABLE",
    "current_plan_name": "<PERSON>âng cao",
    "last_login": "2025-07-08T03:28:23.478Z"
  }
}
```

### PATCH /user/update-profile

#### Request
```http
PATCH /user/update-profile HTTP/1.1
Authorization: Bearer teable_accWhxU2ZkU3O9brgx8_hprrcGxBozswLxEaJSW6J5MGhQ3BmuGf92NG3xGkON0=
Content-Type: application/json

{
  "business_name": "Công ty Cổ phần CUBABLE Updated"
}
```

#### Response (200 OK)
```json
{
  "status": "success",
  "message": "Cập nhật thông tin thành công",
  "data": {
    "username": "27102001",
    "business_name": "Công ty Cổ phần CUBABLE Updated",
    "current_plan_name": "Nâng cao",
    "last_login": "2025-07-08T03:28:23.478Z"
  }
}
```

## Technical Implementation

### Files Created
- `app/schemas/user_profile.py` - Pydantic schemas for request/response
- `app/services/user_profile_service.py` - Business logic and Teable integration
- `app/routes/user_profile.py` - FastAPI route definitions
- Updated `app/main.py` - Router registration

### Authentication Flow
1. Extract Teable access_token from Authorization header
2. Validate Bearer token format
3. Query user table using access_token filter
4. Return user profile or update as requested
5. Handle authentication errors gracefully

### Teable Integration

#### GET User Profile Query
```
GET /table/{TEABLE_TABLE_ID}/record
Parameters:
- fieldKeyType: "dbFieldName"
- viewId: "viw6ye3dhnsRIJXAV4p"
- filter: {"conjunction":"and","filterSet":[{"fieldId":"access_token","operator":"is","value":"user_token"}]}
```

#### PATCH Update Profile
```
PATCH /table/{TEABLE_TABLE_ID}/record/{recordId}
Payload:
{
  "fieldKeyType": "dbFieldName",
  "typecast": true,
  "record": {
    "fields": {
      "business_name": "Updated Business Name"
    }
  }
}
```

## Key Features

### Smart Field Updates
- Only fields with non-null values are included in update payload
- If no fields are provided for update, returns current profile without changes
- Automatically excludes empty or null fields from Teable API calls

### Editable Fields
Currently, only the following field can be edited:
- `business_name` (string) - Company/business name

### Error Handling
- **401 Unauthorized**: Missing, invalid, or empty Authorization header
- **404 Not Found**: User not found with provided access_token
- **400 Bad Request**: Teable API errors
- **500 Internal Server Error**: Unexpected server errors

All error messages are provided in Vietnamese for better user experience.

## Usage Examples

### cURL Examples

#### Get User Profile
```bash
curl -X GET "http://localhost:8001/user/me" \
  -H "Authorization: Bearer teable_accWhxU2ZkU3O9brgx8_hprrcGxBozswLxEaJSW6J5MGhQ3BmuGf92NG3xGkON0="
```

#### Update Profile
```bash
curl -X PATCH "http://localhost:8001/user/update-profile" \
  -H "Authorization: Bearer teable_accWhxU2ZkU3O9brgx8_hprrcGxBozswLxEaJSW6J5MGhQ3BmuGf92NG3xGkON0=" \
  -H "Content-Type: application/json" \
  -d '{
    "business_name": "Công ty Cổ phần CUBABLE Updated"
  }'
```

### JavaScript/Fetch Examples

#### Get User Profile
```javascript
const response = await fetch('http://localhost:8001/user/me', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer teable_accWhxU2ZkU3O9brgx8_hprrcGxBozswLxEaJSW6J5MGhQ3BmuGf92NG3xGkON0='
  }
});

const userProfile = await response.json();
console.log(userProfile.data.business_name);
```

#### Update Profile
```javascript
const response = await fetch('http://localhost:8001/user/update-profile', {
  method: 'PATCH',
  headers: {
    'Authorization': 'Bearer teable_accWhxU2ZkU3O9brgx8_hprrcGxBozswLxEaJSW6J5MGhQ3BmuGf92NG3xGkON0=',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    business_name: 'Công ty Cổ phần CUBABLE Updated'
  })
});

const updatedProfile = await response.json();
console.log(updatedProfile.data.business_name);
```

## Configuration Requirements

### Environment Variables
- `TEABLE_BASE_URL` - Base URL for Teable API
- `TEABLE_TOKEN` - Bearer token for Teable authentication
- `TEABLE_TABLE_ID` - User table ID in Teable

### Constants
- `USER_PROFILE_VIEW_ID = "viw6ye3dhnsRIJXAV4p"` - View ID for user profile queries

## Security Considerations

- Access tokens are validated on every request
- Only authenticated users can access their own profile
- Update operations are limited to editable fields only
- All API calls use HTTPS in production
- Comprehensive input validation and sanitization

## Benefits

- ✅ Secure authentication using access_token
- ✅ Direct Teable integration with proper filtering
- ✅ Only editable fields can be updated
- ✅ Null/empty fields automatically excluded from updates
- ✅ Complete user profile returned after updates
- ✅ Comprehensive error handling with Vietnamese messages
- ✅ Proper HTTP status codes for all scenarios

## Future Enhancements

Potential fields that could be made editable in future versions:
- Email address
- Phone number
- Profile picture
- Notification preferences
- Language settings

## Notes

- Currently only `business_name` field is editable
- Uses viewId `viw6ye3dhnsRIJXAV4p` for user queries
- Access token must be provided in Authorization header
- Update payload only includes non-null fields
- Returns updated profile immediately after changes
