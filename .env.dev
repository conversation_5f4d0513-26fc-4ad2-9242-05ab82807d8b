# Teable API Configuration
TEABLE_BASE_URL=https://app.teable.vn/api
# TEABLE_TOKEN=Bearer teable_acc4GJR3PX5yIdlP0fw_T0VgtCm7s67F0+AKa+w5G3+w0ii5DKSmrQVB+zVqJDU=
# TEABLE_TOKEN=Bearer teable_accTCwlSeXDGPYO6qdL_vpLjfV9dw3gtpRIQu1PWVQ5QJBpI+ToGqFEmfInhPFk=
TEABLE_TOKEN=Bearer teable_accAFr0SCGDTUqXQTQb_+7LBL2ZrQJH/EN6utEyKq057Q0SEfVVFqrn0iDAu9aw=
TEABLE_TABLE_ID=tblj52nsIFcIWDAW4fr

# Invoice API Configuration (Fallback URLs - now using dynamic URLs from user config)
CREATE_INVOICE_URL=https://api-vinvoice.viettel.vn/services/einvoiceapplication/api/InvoiceAPI/InvoiceWS/createInvoice
GET_PDF_URL=https://api-vinvoice.viettel.vn/services/einvoiceapplication/api/InvoiceAPI/InvoiceUtilsWS/getInvoiceRepresentationFile

# OpenRouter API Configuration
OPENROUTER_API_KEY=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

# Server Configuration
PORT=8000
HOST=0.0.0.0