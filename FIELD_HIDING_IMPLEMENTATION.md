# Field Hiding Implementation

## Overview

Implemented automatic hiding of reverse link fields in the product table during the signup process. This feature cleans up the product table view by hiding auto-generated reverse relationship fields that are created due to bidirectional links with import slip details and delivery note details tables.

## Problem Solved

### Before Implementation
When bidirectional relationships are created between:
- Product Table ↔ Import Slip Details Table
- Product Table ↔ Delivery Note Details Table

Teable automatically creates reverse link fields in the Product table, such as:
- "Chi Tiế<PERSON>" (Import Slip Details)
- "Chi Tiết Phiếu Xuất" (Delivery Note Details)

These fields clutter the product table view and are not typically needed for day-to-day product management.

### After Implementation
✅ Reverse link fields are automatically hidden in the product table view
✅ Product table shows only essential product information
✅ Data relationships are maintained but UI is cleaner
✅ Professional appearance for end users

## Technical Implementation

### 1. New Function Added

**File:** `app/services/auth_service.py`

```python
async def hide_reverse_link_fields_in_product_table(product_table_id: str, headers: dict):
    """Hide reverse link fields in product table to clean up the view"""
    try:
        # Get all fields from product table
        fields_url = f"{settings.TEABLE_BASE_URL}/table/{product_table_id}/field"
        fields_result = handle_teable_api_call("GET", fields_url, headers=headers)
        
        fields = fields_result.get("data", [])
        fields_to_hide = []
        
        # Find reverse link fields
        for field in fields:
            field_name = field.get("name", "")
            field_type = field.get("type", "")
            field_id = field.get("id", "")
            
            # Hide fields that link back to import slip details and delivery note details
            if (field_type == "link" and 
                ("Chi Tiết Phiếu Nhập" in field_name or 
                 "Chi Tiết Phiếu Xuất" in field_name or
                 "import_slip_details" in field_name.lower() or
                 "delivery_note_details" in field_name.lower())):
                fields_to_hide.append({
                    "fieldId": field_id,
                    "columnMeta": {"hidden": True}
                })
        
        # Get default view and hide fields
        views_url = f"{settings.TEABLE_BASE_URL}/table/{product_table_id}/view"
        views_result = handle_teable_api_call("GET", views_url, headers=headers)
        
        views = views_result.get("data", [])
        default_view_id = views[0].get("id", "")
        
        # Hide the fields using Teable API
        hide_url = f"{settings.TEABLE_BASE_URL}/table/{product_table_id}/view/{default_view_id}/column-meta"
        hide_result = handle_teable_api_call("PUT", hide_url, data=json.dumps(fields_to_hide), headers=headers)
        
    except Exception as e:
        logger.error(f"Error hiding reverse link fields: {str(e)}")
```

### 2. Integration into Signup Process

**Location:** Step 11.5 in signup process

```python
# Step 11: Add inventory tracking fields to product table
await add_inventory_tracking_fields_to_product(product_table_id, import_slip_details_id, delivery_note_details_id, space_headers)

# Step 11.5: Hide reverse link fields in product table for cleaner view
await hide_reverse_link_fields_in_product_table(product_table_id, space_headers)

# Step 12: Update user record with all table IDs and access token
```

### 3. API Call Structure

Based on the provided curl example, the function uses:

**Endpoint:** `PUT /table/{table_id}/view/{view_id}/column-meta`

**Request Body:**
```json
[
  {
    "fieldId": "fldZA9wRpaHGe19wjzA",
    "columnMeta": {
      "hidden": true
    }
  }
]
```

**Headers:**
- `Content-Type: application/json`
- `Authorization: Bearer {access_token}`

## Field Detection Logic

### Fields to Hide

The function automatically detects and hides fields that match these criteria:

1. **Field Type:** `link` (relationship field)
2. **Field Name Contains:**
   - "Chi Tiết Phiếu Nhập" (Vietnamese for Import Slip Details)
   - "Chi Tiết Phiếu Xuất" (Vietnamese for Delivery Note Details)
   - "import_slip_details" (English lowercase)
   - "delivery_note_details" (English lowercase)

### Detection Algorithm

```python
if (field_type == "link" and 
    ("Chi Tiết Phiếu Nhập" in field_name or 
     "Chi Tiết Phiếu Xuất" in field_name or
     "import_slip_details" in field_name.lower() or
     "delivery_note_details" in field_name.lower())):
    # Hide this field
```

## Benefits

### ✅ **User Experience**
- **Cleaner Interface:** Product table shows only relevant fields
- **Reduced Confusion:** Users don't see technical reverse relationship fields
- **Professional Appearance:** Tables look more polished and organized
- **Focus on Essentials:** Attention directed to important product information

### ✅ **Data Integrity**
- **Relationships Maintained:** Data connections remain intact
- **Functionality Preserved:** All business logic continues to work
- **No Data Loss:** Fields are hidden, not deleted
- **Reversible:** Fields can be unhidden if needed

### ✅ **Business Value**
- **Better Adoption:** Users more likely to use clean, organized interfaces
- **Reduced Training:** Less explanation needed about technical fields
- **Improved Productivity:** Faster navigation through essential fields
- **Professional Image:** System appears more polished to end users

## Product Table View Comparison

### Before Field Hiding
```
Product Table Fields:
├── Mã sản phẩm (Product Code)
├── Tên sản phẩm (Product Name)
├── Đơn vị tính (Unit Conversions)
├── Tổng nhập (Total Imported)
├── Tổng xuất (Total Delivered)
├── Tồn kho hiện tại (Current Inventory)
├── Chi Tiết Phiếu Nhập (Import Slip Details) ❌ CLUTTER
└── Chi Tiết Phiếu Xuất (Delivery Note Details) ❌ CLUTTER
```

### After Field Hiding
```
Product Table Fields:
├── Mã sản phẩm (Product Code)
├── Tên sản phẩm (Product Name)
├── Đơn vị tính (Unit Conversions)
├── Tổng nhập (Total Imported)
├── Tổng xuất (Total Delivered)
└── Tồn kho hiện tại (Current Inventory)
    
Hidden Fields (still functional):
├── Chi Tiết Phiếu Nhập (Import Slip Details) ✅ HIDDEN
└── Chi Tiết Phiếu Xuất (Delivery Note Details) ✅ HIDDEN
```

## Error Handling

### Graceful Degradation
- If field hiding fails, signup process continues
- Warnings logged but don't block account creation
- System remains functional even if hiding doesn't work

### Logging
```python
logger.info(f"Marking field '{field_name}' ({field_id}) for hiding")
logger.info(f"Successfully hid {len(fields_to_hide)} reverse link fields in product table")
logger.warning(f"Failed to hide fields in product table: {error}")
```

## Testing

### Test File: `test_field_hiding.py`

**Test Coverage:**
- ✅ Signup process includes field hiding
- ✅ Function integration verification
- ✅ Error handling validation
- ✅ User experience improvement confirmation

**Run Test:**
```bash
python test_field_hiding.py
```

## Configuration

### Customizable Field Detection

To modify which fields are hidden, update the detection logic:

```python
# Current logic
if (field_type == "link" and 
    ("Chi Tiết Phiếu Nhập" in field_name or 
     "Chi Tiết Phiếu Xuất" in field_name)):

# Add more patterns as needed
if (field_type == "link" and 
    ("Chi Tiết Phiếu Nhập" in field_name or 
     "Chi Tiết Phiếu Xuất" in field_name or
     "Your Custom Pattern" in field_name)):
```

### View Selection

Currently hides fields in the default view (first view). To target specific views:

```python
# Find specific view by name
target_view = next((v for v in views if v.get("name") == "Main View"), views[0])
```

## Future Enhancements

### Potential Improvements
1. **Configuration File:** Make field patterns configurable
2. **Multiple Views:** Hide fields in all views, not just default
3. **User Preferences:** Allow users to show/hide fields as needed
4. **Bulk Operations:** Hide multiple field types in one API call
5. **Undo Functionality:** Provide easy way to unhide fields

### API Extensions
1. **Hide Fields Endpoint:** Dedicated API for field visibility management
2. **View Management:** APIs for complete view customization
3. **Field Templates:** Predefined field visibility templates

## Summary

The field hiding implementation provides:

✅ **Automatic cleanup** of product table views during signup
✅ **Professional appearance** by hiding technical reverse relationship fields
✅ **Maintained functionality** while improving user experience
✅ **Graceful error handling** that doesn't break the signup process
✅ **Configurable detection** for different field patterns
✅ **Complete integration** into the existing signup workflow

This enhancement significantly improves the user experience by presenting clean, focused product tables while maintaining all underlying data relationships and functionality! 🎯📋
