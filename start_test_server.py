#!/usr/bin/env python3
"""
Start FastAPI server for testing
"""
import subprocess
import sys
import time
import requests
from threading import Thread

def start_server():
    """Start the FastAPI server"""
    try:
        print("🚀 Starting FastAPI server...")
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "app.main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000",
            "--reload"
        ])
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {str(e)}")

def check_server_health():
    """Check if server is running"""
    try:
        response = requests.get("http://localhost:8000/")
        if response.status_code == 200:
            print("✅ Server is running and healthy")
            return True
    except requests.exceptions.ConnectionError:
        print("❌ Server is not responding")
    return False

def wait_for_server(max_wait=30):
    """Wait for server to start"""
    print("⏳ Waiting for server to start...")
    for i in range(max_wait):
        if check_server_health():
            return True
        time.sleep(1)
        print(f"   Waiting... ({i+1}/{max_wait})")
    return False

if __name__ == "__main__":
    print("🧪 FastAPI Test Server Starter")
    print("=" * 40)
    
    # Check if server is already running
    if check_server_health():
        print("✅ Server is already running!")
    else:
        print("🚀 Starting new server instance...")
        
        # Start server in background thread
        server_thread = Thread(target=start_server, daemon=True)
        server_thread.start()
        
        # Wait for server to be ready
        if wait_for_server():
            print("✅ Server is ready for testing!")
            print("\n📋 Available endpoints:")
            print("   • http://localhost:8000/ - Root endpoint")
            print("   • http://localhost:8000/docs - API documentation")
            print("   • http://localhost:8000/redoc - Alternative docs")
            
            print("\n🧪 To run tests:")
            print("   python run_complete_api_test.py")
            
            print("\n⏹️ Press Ctrl+C to stop server")
            
            try:
                # Keep main thread alive
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 Shutting down...")
        else:
            print("❌ Server failed to start within timeout")
            sys.exit(1)
