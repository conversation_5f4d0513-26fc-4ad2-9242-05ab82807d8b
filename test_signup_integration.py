#!/usr/bin/env python3
"""
Test script to verify signup functionality with VietQR API integration
"""
import requests
import json
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:8000"

def test_signup_valid_taxcode():
    """Test signup with valid taxcode"""
    print("🧪 Testing signup with valid taxcode...")
    
    test_signup_data = {
        "username": "0316316874",  # CUBABLE JSC taxcode
        "password": "test_password_123"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/signup",
            json=test_signup_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Signup successful!")
            print(f"📝 Business Name: {data.get('business_name', 'N/A')}")
            print(f"📝 Upload File ID: {data.get('upload_file_id', 'N/A')}")
            print(f"📝 Table Order ID: {data.get('table_order_id', 'N/A')}")
        else:
            print("❌ Signup failed")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to server. Make sure it's running on port 8000")
    except Exception as e:
        print(f"❌ Error: {e}")

def test_signup_invalid_taxcode():
    """Test signup with invalid taxcode"""
    print("🧪 Testing signup with invalid taxcode...")
    
    test_signup_data = {
        "username": "1234567890",  # Invalid taxcode
        "password": "test_password_123"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/signup",
            json=test_signup_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        if response.status_code == 400:
            print("✅ Invalid taxcode test passed!")
            print("Expected error response received")
        else:
            print("❌ Invalid taxcode test failed!")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to server. Make sure it's running on port 8000")
    except Exception as e:
        print(f"❌ Error: {e}")

def test_root():
    """Test root endpoint"""
    print("🧪 Testing root endpoint...")
    
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✅ Root endpoint working!")
        else:
            print("❌ Root endpoint failed")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to server. Make sure it's running on port 8000")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🚀 Starting signup integration tests...")
    print(f"⏰ Current time: {datetime.now().isoformat()}")
    print("-" * 60)
    
    test_root()
    print("-" * 60)
    test_signup_valid_taxcode()
    print("-" * 60)
    test_signup_invalid_taxcode()
    print("-" * 60)
    print("✅ Tests completed!")
    print("\n📝 Note: If signup was successful, the user record should now contain:")
    print("   - business_name from VietQR API")
    print("   - upload_file_id for the invoice_file field")
    print("   - All table IDs for the created workspace")
