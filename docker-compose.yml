version: '3.8'

services:
  order-voice-backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nola-backend
    ports:
      - "8001:8001"
    environment:
      - PORT=8000
      - PYTHONUNBUFFERED=1
    env_file:
      - .env.v2
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - order-voice-network

networks:
  order-voice-network:
    driver: bridge