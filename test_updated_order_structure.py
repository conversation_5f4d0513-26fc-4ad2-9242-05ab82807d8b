#!/usr/bin/env python3
"""
Test Updated Order API with Refactored Table Structure
- Order details now link to product table instead of using product_name
- Order details include unit_conversions link with isOneWay=True
- Exact dbFieldNames used throughout the process
- Automatic delivery note creation with same structure
"""
import json
import requests
import time

class UpdatedOrderStructureTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.access_token = None
        self.created_customer_id = None
        self.created_product_id = None
        self.created_unit_conversion_id = None
        
    def log_step(self, step_name, description):
        """Log test step"""
        print(f"\n{'='*80}")
        print(f"🧪 STEP: {step_name}")
        print(f"📝 {description}")
        print(f"{'='*80}")
        
    def log_api_call(self, method, endpoint, data=None):
        """Log API call details"""
        print(f"\n📡 API CALL: {method} {endpoint}")
        if data:
            print(f"📋 Request Data: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
    def log_response(self, response):
        """Log API response"""
        print(f"\n📊 RESPONSE:")
        print(f"Status Code: {response.status_code}")
        
        try:
            response_data = response.json()
            print(f"Response Data: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
            
            if response.status_code < 300:
                print("✅ SUCCESS")
            else:
                print("❌ FAILED")
                
            return response_data
            
        except json.JSONDecodeError:
            print(f"Raw Response: {response.text}")
            print("❌ FAILED - Invalid JSON")
            return None
            
    def test_signup(self):
        """Test user signup with updated table structure"""
        self.log_step("USER_SIGNUP", "Create business account with updated order detail structure")
        
        signup_data = {
            "username": "**********",
            "password": "cubable2025"
        }
        
        self.log_api_call("POST", "/auth/signup", signup_data)
        
        response = requests.post(f"{self.base_url}/auth/signup", json=signup_data)
        result = self.log_response(response)
        
        if result and response.status_code < 300:
            print(f"🏢 Business: {result.get('business_name', 'N/A')}")
            print(f"📊 Tables Created: {len(result.get('tables', {}))}")
            print("🔍 Enhanced order detail table includes:")
            print("   • product_link field (link to product table)")
            print("   • unit_conversions field (link to unit conversions table)")
            print("   • isOneWay=True for order detail table only")
            print("   • All calculation fields (temp_total, final_total)")
            return True
        return False
        
    def test_signin(self):
        """Test user signin"""
        self.log_step("USER_SIGNIN", "Authenticate and get access token")
        
        signin_data = {
            "username": "**********",
            "password": "cubable2025"
        }
        
        self.log_api_call("POST", "/auth/signin", signin_data)
        
        response = requests.post(f"{self.base_url}/auth/signin", json=signin_data)
        result = self.log_response(response)
        
        if result and response.status_code == 200:
            self.access_token = result.get("accessToken")
            print(f"🔑 Access Token: {self.access_token[:30]}...")
            return True
        return False
        
    def test_create_customer(self):
        """Test creating customer"""
        self.log_step("CREATE_CUSTOMER", "Create customer for order")
        
        if not self.access_token:
            print("❌ No access token - skipping test")
            return False
            
        customer_data = {
            "customer_name": "Công ty TNHH XYZ",
            "phone": "0901234567",
            "email": "<EMAIL>",
            "address": "456 Đường XYZ, Quận 2, TP.HCM"
        }
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        self.log_api_call("POST", "/customers/create-customer", customer_data)
        
        response = requests.post(f"{self.base_url}/customers/create-customer", json=customer_data, headers=headers)
        result = self.log_response(response)
        
        if result and response.status_code == 200:
            self.created_customer_id = result.get('customer_id')
            print(f"👤 Created Customer ID: {self.created_customer_id}")
            return True
        return False
        
    def test_create_product_with_units(self):
        """Test creating product with unit conversions"""
        self.log_step("CREATE_PRODUCT_WITH_UNITS", "Create product with unit conversions")
        
        if not self.access_token:
            print("❌ No access token - skipping test")
            return False
            
        product_data = {
            "product_name": "Nước ngọt Sprite 330ml",
            "unit_conversions": [
                {
                    "name_unit": "Chai",
                    "conversion_factor": 1,
                    "unit_default": "Chai",
                    "price": 15000,
                    "vat": 10.0
                },
                {
                    "name_unit": "Lốc 24 chai",
                    "conversion_factor": 24,
                    "unit_default": "Chai",
                    "price": 350000,
                    "vat": 8.0
                }
            ]
        }
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        self.log_api_call("POST", "/products/create-product-with-units", product_data)
        
        response = requests.post(f"{self.base_url}/products/create-product-with-units", json=product_data, headers=headers)
        result = self.log_response(response)
        
        if result and response.status_code == 200:
            product_info = result.get('product_data', {})
            created_units = result.get('created_unit_conversions', [])
            
            self.created_product_id = product_info.get('product_id')
            # Get the first unit conversion ID for testing
            if created_units:
                self.created_unit_conversion_id = created_units[0].get('unit_conversion_id')
            
            print(f"🥤 Created Product ID: {self.created_product_id}")
            print(f"⚖️ Created Unit Conversion ID: {self.created_unit_conversion_id}")
            return True
        return False
        
    def test_create_order_with_updated_structure(self):
        """Test creating order with updated table structure"""
        self.log_step("CREATE_ORDER_UPDATED_STRUCTURE", "Create order with new link-based structure")
        
        if not all([self.access_token, self.created_customer_id, self.created_product_id, self.created_unit_conversion_id]):
            print("❌ Prerequisites not met - skipping test")
            return False
            
        order_data = {
            "customer_id": self.created_customer_id,
            "order_details": [
                {
                    "product_id": self.created_product_id,
                    "unit_conversions_id": self.created_unit_conversion_id,
                    "unit_price": 15000,
                    "quantity": 100,
                    "vat": 10
                }
            ],
            "delivery_type": "Xuất bán"
        }
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        self.log_api_call("POST", "/orders/create-order", order_data)
        
        response = requests.post(f"{self.base_url}/orders/create-order", json=order_data, headers=headers)
        result = self.log_response(response)
        
        if result and response.status_code == 200:
            print(f"\n📊 ORDER & DELIVERY NOTE CREATED:")
            print(f"📋 Order ID: {result.get('order_id', 'N/A')}")
            print(f"📋 Order Code: {result.get('order_code', 'N/A')}")
            print(f"📤 Delivery Note ID: {result.get('delivery_note_id', 'N/A')}")
            print(f"📤 Delivery Note Code: {result.get('delivery_note_code', 'N/A')}")
            print(f"👤 Customer ID: {result.get('customer_id', 'N/A')}")
            print(f"📦 Total Items: {result.get('total_items', 0)}")
            print(f"💰 Total Temp: {result.get('total_temp', 0):,} VND")
            print(f"💰 Total VAT: {result.get('total_vat', 0):,} VND")
            print(f"💰 Total After VAT: {result.get('total_after_vat', 0):,} VND")
            
            print(f"\n🔍 STRUCTURE IMPROVEMENTS:")
            print(f"   ✅ Order details use product_link (not product_name)")
            print(f"   ✅ Order details include unit_conversions link")
            print(f"   ✅ isOneWay=True only for order detail table")
            print(f"   ✅ Exact dbFieldNames used throughout")
            print(f"   ✅ Delivery note details match order structure")
            print(f"   ✅ All calculation fields included")
            
            return True
        return False
        
    def run_complete_test(self):
        """Run complete updated order structure test"""
        print("🚀 STARTING UPDATED ORDER STRUCTURE TEST")
        print("=" * 80)
        
        try:
            # Run tests in sequence
            tests = [
                ("Signup with Updated Structure", self.test_signup),
                ("Signin", self.test_signin),
                ("Create Customer", self.test_create_customer),
                ("Create Product with Units", self.test_create_product_with_units),
                ("Create Order with Updated Structure", self.test_create_order_with_updated_structure)
            ]
            
            results = {}
            
            for test_name, test_func in tests:
                success = test_func()
                results[test_name] = success
                
                if not success and test_name in ["Signup with Updated Structure", "Signin"]:
                    print(f"❌ Critical test '{test_name}' failed - stopping")
                    break
                    
                time.sleep(1)  # Brief pause between tests
                
            # Generate summary
            self.generate_summary(results)
            
        except Exception as e:
            print(f"❌ Test flow error: {str(e)}")
            
    def generate_summary(self, results):
        """Generate test summary"""
        self.log_step("TEST_SUMMARY", "Updated Order Structure Test Results")
        
        print("📊 TEST RESULTS:")
        print("-" * 50)
        
        for test_name, success in results.items():
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {test_name}")
            
        passed = sum(1 for success in results.values() if success)
        total = len(results)
        
        print(f"\n📈 Summary: {passed}/{total} tests passed ({(passed/total*100):.1f}%)")
        
        print("\n🔄 STRUCTURE UPDATES COMPLETED:")
        print("-" * 40)
        updates = [
            "✅ Order details: product_name → product_link (link field)",
            "✅ Order details: Added unit_conversions link field",
            "✅ Order detail link fields use isOneWay=True",
            "✅ Delivery note details have bidirectional links",
            "✅ Import slip details have bidirectional links",
            "✅ All tables use exact dbFieldNames",
            "✅ Calculation fields included (temp_total, final_total)"
        ]
        
        for update in updates:
            print(f"   {update}")
            
        print("\n📦 BUSINESS BENEFITS:")
        print("-" * 25)
        benefits = [
            "🔗 Proper relational data structure",
            "📊 Consistent field naming across tables",
            "⚖️ Unit conversion tracking in all transactions",
            "🛡️ Data integrity with foreign key relationships",
            "🔄 Automatic delivery note creation",
            "📋 Complete order fulfillment workflow"
        ]
        
        for benefit in benefits:
            print(f"   {benefit}")

if __name__ == "__main__":
    tester = UpdatedOrderStructureTester()
    tester.run_complete_test()
