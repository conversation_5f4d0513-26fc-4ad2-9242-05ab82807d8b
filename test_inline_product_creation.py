#!/usr/bin/env python3
"""
Test Inline Product Creation with Unit Conversions
"""
import json
import requests
import time

class InlineProductCreationTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.access_token = None
        
    def log_step(self, step_name, description):
        """Log test step"""
        print(f"\n{'='*80}")
        print(f"🧪 STEP: {step_name}")
        print(f"📝 {description}")
        print(f"{'='*80}")
        
    def log_api_call(self, method, endpoint, data=None):
        """Log API call details"""
        print(f"\n📡 API CALL: {method} {endpoint}")
        if data:
            print(f"📋 Request Data: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
    def log_response(self, response):
        """Log API response"""
        print(f"\n📊 RESPONSE:")
        print(f"Status Code: {response.status_code}")
        
        try:
            response_data = response.json()
            print(f"Response Data: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
            
            if response.status_code < 300:
                print("✅ SUCCESS")
            else:
                print("❌ FAILED")
                
            return response_data
            
        except json.JSONDecodeError:
            print(f"Raw Response: {response.text}")
            print("❌ FAILED - Invalid JSON")
            return None
            
    def test_signup(self):
        """Test user signup"""
        self.log_step("USER_SIGNUP", "Create business account")
        
        signup_data = {
            "username": "**********",
            "password": "cubable2025"
        }
        
        self.log_api_call("POST", "/auth/signup", signup_data)
        
        response = requests.post(f"{self.base_url}/auth/signup", json=signup_data)
        result = self.log_response(response)
        
        if result and response.status_code < 300:
            print(f"🏢 Business: {result.get('business_name', 'N/A')}")
            return True
        return False
        
    def test_signin(self):
        """Test user signin"""
        self.log_step("USER_SIGNIN", "Authenticate and get access token")
        
        signin_data = {
            "username": "**********",
            "password": "cubable2025"
        }
        
        self.log_api_call("POST", "/auth/signin", signin_data)
        
        response = requests.post(f"{self.base_url}/auth/signin", json=signin_data)
        result = self.log_response(response)
        
        if result and response.status_code == 200:
            self.access_token = result.get("accessToken")
            print(f"🔑 Access Token: {self.access_token[:30]}...")
            return True
        return False
        
    def test_create_beverage_product(self):
        """Test creating beverage product with inline units"""
        self.log_step("CREATE_BEVERAGE_PRODUCT", "Create Coca Cola with Chai/Lốc/Thùng units")
        
        if not self.access_token:
            print("❌ No access token - skipping test")
            return False
            
        product_data = {
            "product_name": "Nước ngọt Coca Cola 330ml",
            "unit_price": 15000,
            "vat_rate": 10,
            "unit_conversions": [
                {
                    "name_unit": "Chai",
                    "conversion_factor": 1,
                    "unit_default": "Chai",
                    "price": 15000
                },
                {
                    "name_unit": "Lốc",
                    "conversion_factor": 24,
                    "unit_default": "Chai",
                    "price": 350000
                },
                {
                    "name_unit": "Thùng",
                    "conversion_factor": 288,
                    "unit_default": "Chai",
                    "price": 4200000
                }
            ]
        }
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        self.log_api_call("POST", "/products/create-product-with-units", product_data)
        
        response = requests.post(f"{self.base_url}/products/create-product-with-units", json=product_data, headers=headers)
        result = self.log_response(response)
        
        if result and response.status_code == 200:
            product_data = result.get('product_data', {})
            created_units = result.get('created_unit_conversions', [])
            
            print(f"📦 Created Product: {product_data.get('product_name', 'N/A')}")
            print(f"💰 Price: {product_data.get('unit_price', 0):,} VND per Chai")
            print(f"📊 Inventory: {product_data.get('inventory', 0)} Chai")
            print(f"⚖️ Created {len(created_units)} Unit Conversions:")
            
            for unit in created_units:
                print(f"   • {unit.get('name_unit')} (Factor: {unit.get('conversion_factor')}, Default: {unit.get('unit_default')})")
            
            self.beverage_product_id = product_data.get('product_id')
            return True
        return False
        
    def test_create_electronics_product(self):
        """Test creating electronics product with inline units"""
        self.log_step("CREATE_ELECTRONICS_PRODUCT", "Create laptop with Chiếc/Bộ units")
        
        if not self.access_token:
            print("❌ No access token - skipping test")
            return False
            
        product_data = {
            "product_name": "Laptop Dell Inspiron 15 3000 Series",
            "unit_price": 15000000,
            "vat_rate": 10,
            "unit_conversions": [
                {
                    "name_unit": "Chiếc",
                    "conversion_factor": 1,
                    "unit_default": "Chiếc",
                    "price": 15000000
                },
                {
                    "name_unit": "Bộ",
                    "conversion_factor": 1,
                    "unit_default": "Chiếc",
                    "price": 15000000
                }
            ]
        }
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        self.log_api_call("POST", "/products/create-product-with-units", product_data)
        
        response = requests.post(f"{self.base_url}/products/create-product-with-units", json=product_data, headers=headers)
        result = self.log_response(response)
        
        if result and response.status_code == 200:
            product_data = result.get('product_data', {})
            created_units = result.get('created_unit_conversions', [])
            
            print(f"💻 Created Product: {product_data.get('product_name', 'N/A')}")
            print(f"💰 Price: {product_data.get('unit_price', 0):,} VND per unit")
            print(f"📊 Inventory: {product_data.get('inventory', 0)} units")
            print(f"⚖️ Created {len(created_units)} Unit Conversions:")
            
            for unit in created_units:
                print(f"   • {unit.get('name_unit')} (Factor: {unit.get('conversion_factor')}, Default: {unit.get('unit_default')})")
            
            self.electronics_product_id = product_data.get('product_id')
            return True
        return False
        
    def test_error_handling(self):
        """Test error handling with invalid data"""
        self.log_step("ERROR_HANDLING", "Test rollback on invalid unit conversion")
        
        if not self.access_token:
            print("❌ No access token - skipping test")
            return False
            
        # Test with invalid conversion factor (negative number)
        product_data = {
            "product_name": "Test Product for Error",
            "unit_price": 10000,
            "vat_rate": 10,
            "category": "Test",
            "brand": "Test",
            "inventory": 10,
            "unit_conversions": [
                {
                    "name_unit": "Valid Unit",
                    "conversion_factor": 1,
                    "unit_default": "Valid Unit"
                },
                {
                    "name_unit": "Invalid Unit",
                    "conversion_factor": -1,  # Invalid negative factor
                    "unit_default": "Valid Unit"
                }
            ]
        }
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        self.log_api_call("POST", "/products/create-product-with-units", product_data)
        
        response = requests.post(f"{self.base_url}/products/create-product-with-units", json=product_data, headers=headers)
        result = self.log_response(response)
        
        # This should fail and demonstrate rollback
        if response.status_code >= 400:
            print("✅ Error handling working correctly - operation failed as expected")
            print("🔄 Rollback should have cleaned up any partial creations")
            return True
        else:
            print("❌ Error handling not working - operation should have failed")
            return False
            
    def test_find_created_products(self):
        """Test finding the created products"""
        self.log_step("FIND_CREATED_PRODUCTS", "Search for created products")
        
        if not self.access_token:
            print("❌ No access token - skipping test")
            return False
            
        headers = {"Authorization": f"Bearer {self.access_token}"}
        
        # Search for Coca Cola
        self.log_api_call("GET", "/products/find-by-name?name=Coca Cola")
        response = requests.get(f"{self.base_url}/products/find-by-name?name=Coca Cola", headers=headers)
        result = self.log_response(response)
        
        if result and response.status_code == 200:
            products = result.get("products", [])
            print(f"🥤 Found {len(products)} Coca Cola products")
            
            for product in products:
                print(f"   • {product.get('product_name')} - {product.get('unit_price', 0):,} VND")
                print(f"     Unit Conversions: {len(product.get('unit_conversions', []))}")
            
            return True
        return False
        
    def run_complete_test(self):
        """Run complete inline product creation test"""
        print("🚀 STARTING INLINE PRODUCT CREATION TEST")
        print("=" * 80)
        
        try:
            # Run tests in sequence
            tests = [
                ("Signup", self.test_signup),
                ("Signin", self.test_signin),
                ("Create Beverage Product", self.test_create_beverage_product),
                ("Create Electronics Product", self.test_create_electronics_product),
                ("Error Handling Test", self.test_error_handling),
                ("Find Created Products", self.test_find_created_products)
            ]
            
            results = {}
            
            for test_name, test_func in tests:
                success = test_func()
                results[test_name] = success
                
                if not success and test_name in ["Signup", "Signin"]:
                    print(f"❌ Critical test '{test_name}' failed - stopping")
                    break
                    
                time.sleep(1)  # Brief pause between tests
                
            # Generate summary
            self.generate_summary(results)
            
        except Exception as e:
            print(f"❌ Test flow error: {str(e)}")
            
    def generate_summary(self, results):
        """Generate test summary"""
        self.log_step("TEST_SUMMARY", "Inline Product Creation Test Results")
        
        print("📊 TEST RESULTS:")
        print("-" * 50)
        
        for test_name, success in results.items():
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {test_name}")
            
        passed = sum(1 for success in results.values() if success)
        total = len(results)
        
        print(f"\n📈 Summary: {passed}/{total} tests passed ({(passed/total*100):.1f}%)")
        
        print("\n🎯 INLINE PRODUCT CREATION FEATURES TESTED:")
        print("-" * 55)
        features = [
            "✅ Atomic product + unit conversion creation",
            "✅ Multiple unit conversions per product",
            "✅ Rollback on failure (data consistency)",
            "✅ Vietnamese business product examples",
            "✅ Complex unit conversion relationships",
            "✅ Error handling and validation"
        ]
        
        for feature in features:
            print(f"   {feature}")
            
        print("\n⚖️ BUSINESS BENEFITS:")
        print("-" * 25)
        benefits = [
            "🔄 Single API call for complete product setup",
            "📊 Product-specific unit conversions",
            "⚡ Faster product catalog creation",
            "🛡️ Data consistency with rollback",
            "📋 Simplified workflow for businesses",
            "🎯 Reduced API complexity"
        ]
        
        for benefit in benefits:
            print(f"   {benefit}")

if __name__ == "__main__":
    tester = InlineProductCreationTester()
    tester.run_complete_test()
