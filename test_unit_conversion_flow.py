#!/usr/bin/env python3
"""
Test Unit Conversion Flow with Product Creation
"""
import json
import requests
import time

class UnitConversionFlowTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.access_token = None
        
    def log_step(self, step_name, description):
        """Log test step"""
        print(f"\n{'='*80}")
        print(f"🧪 STEP: {step_name}")
        print(f"📝 {description}")
        print(f"{'='*80}")
        
    def log_api_call(self, method, endpoint, data=None):
        """Log API call details"""
        print(f"\n📡 API CALL: {method} {endpoint}")
        if data:
            print(f"📋 Request Data: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
    def log_response(self, response):
        """Log API response"""
        print(f"\n📊 RESPONSE:")
        print(f"Status Code: {response.status_code}")
        
        try:
            response_data = response.json()
            print(f"Response Data: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
            
            if response.status_code < 300:
                print("✅ SUCCESS")
            else:
                print("❌ FAILED")
                
            return response_data
            
        except json.JSONDecodeError:
            print(f"Raw Response: {response.text}")
            print("❌ FAILED - Invalid JSON")
            return None
            
    def test_signup(self):
        """Test user signup"""
        self.log_step("USER_SIGNUP", "Create business account with unit conversion table")
        
        signup_data = {
            "username": "**********",
            "password": "cubable2025"
        }
        
        self.log_api_call("POST", "/auth/signup", signup_data)
        
        response = requests.post(f"{self.base_url}/auth/signup", json=signup_data)
        result = self.log_response(response)
        
        if result and response.status_code < 300:
            print(f"🏢 Business: {result.get('business_name', 'N/A')}")
            print(f"📊 Tables Created: {len(result.get('tables', {}))}")
            print(f"⚖️ Unit Conversion Table: {result.get('tables', {}).get('unit_conversion_table_id', 'N/A')}")
            return True
        return False
        
    def test_signin(self):
        """Test user signin"""
        self.log_step("USER_SIGNIN", "Authenticate and get access token")
        
        signin_data = {
            "username": "**********",
            "password": "cubable2025"
        }
        
        self.log_api_call("POST", "/auth/signin", signin_data)
        
        response = requests.post(f"{self.base_url}/auth/signin", json=signin_data)
        result = self.log_response(response)
        
        if result and response.status_code == 200:
            self.access_token = result.get("accessToken")
            print(f"🔑 Access Token: {self.access_token[:30]}...")
            return True
        return False
        
    def test_get_unit_conversions(self):
        """Test getting unit conversions"""
        self.log_step("GET_UNIT_CONVERSIONS", "Retrieve available unit conversions")
        
        if not self.access_token:
            print("❌ No access token - skipping test")
            return False
            
        headers = {"Authorization": f"Bearer {self.access_token}"}
        self.log_api_call("GET", "/unit-conversions/list")
        
        response = requests.get(f"{self.base_url}/unit-conversions/list", headers=headers)
        result = self.log_response(response)
        
        if result and response.status_code == 200:
            unit_conversions = result.get("unit_conversions", [])
            print(f"⚖️ Available Unit Conversions: {len(unit_conversions)}")
            
            for unit in unit_conversions:
                print(f"   • {unit.get('name_unit')} (Factor: {unit.get('conversion_factor')}, Default: {unit.get('unit_default')})")
            
            # Store unit conversion IDs for product creation
            self.unit_conversion_ids = [unit.get('unit_conversion_id') for unit in unit_conversions[:2]]
            return True
        return False
        
    def test_create_unit_conversion(self):
        """Test creating custom unit conversion"""
        self.log_step("CREATE_UNIT_CONVERSION", "Create custom unit conversion")
        
        if not self.access_token:
            print("❌ No access token - skipping test")
            return False
            
        unit_conversion_data = {
            "name_unit": "Pallet",
            "conversion_factor": 48,
            "unit_default": "Thùng"
        }
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        self.log_api_call("POST", "/unit-conversions/create-unit-conversion", unit_conversion_data)
        
        response = requests.post(f"{self.base_url}/unit-conversions/create-unit-conversion", json=unit_conversion_data, headers=headers)
        result = self.log_response(response)
        
        if result and response.status_code == 200:
            print(f"⚖️ Created Unit Conversion: {result.get('unit_conversion_data', {}).get('name_unit', 'N/A')}")
            return True
        return False
        
    def test_create_product_with_units(self):
        """Test creating product with unit conversions"""
        self.log_step("CREATE_PRODUCT_WITH_UNITS", "Create product linked to unit conversions")
        
        if not self.access_token:
            print("❌ No access token - skipping test")
            return False
            
        if not hasattr(self, 'unit_conversion_ids'):
            print("❌ No unit conversion IDs available - skipping test")
            return False
            
        product_data = {
            "product_name": "Laptop Dell Inspiron 15 3000",
            "unit_price": 15000000,
            "unit_conversions": self.unit_conversion_ids,
            "vat_rate": 10
        }
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        self.log_api_call("POST", "/products/create-product", product_data)
        
        response = requests.post(f"{self.base_url}/products/create-product", json=product_data, headers=headers)
        result = self.log_response(response)
        
        if result and response.status_code == 200:
            product_data = result.get('product_data', {})
            print(f"📦 Created Product: {product_data.get('product_name', 'N/A')}")
            print(f"💰 Price: {product_data.get('unit_price', 0):,} VND")
            print(f"⚖️ Unit Conversions: {product_data.get('unit_conversions', [])}")
            print(f"📊 Inventory: {product_data.get('inventory', 0)}")
            return True
        return False
        
    def test_find_products(self):
        """Test finding products"""
        self.log_step("FIND_PRODUCTS", "Search for products by name")
        
        if not self.access_token:
            print("❌ No access token - skipping test")
            return False
            
        headers = {"Authorization": f"Bearer {self.access_token}"}
        self.log_api_call("GET", "/products/find-by-name?name=Laptop Dell")
        
        response = requests.get(f"{self.base_url}/products/find-by-name?name=Laptop Dell", headers=headers)
        result = self.log_response(response)
        
        if result and response.status_code == 200:
            products = result.get("products", [])
            print(f"📦 Found Products: {len(products)}")
            
            for product in products:
                print(f"   • {product.get('product_name')} - {product.get('unit_price', 0):,} VND")
                print(f"     Units: {product.get('unit_conversions', [])}")
            
            return True
        return False
        
    def run_complete_flow(self):
        """Run complete unit conversion flow test"""
        print("🚀 STARTING UNIT CONVERSION FLOW TEST")
        print("=" * 80)
        
        try:
            # Run tests in sequence
            tests = [
                ("Signup", self.test_signup),
                ("Signin", self.test_signin),
                ("Get Unit Conversions", self.test_get_unit_conversions),
                ("Create Custom Unit", self.test_create_unit_conversion),
                ("Create Product with Units", self.test_create_product_with_units),
                ("Find Products", self.test_find_products)
            ]
            
            results = {}
            
            for test_name, test_func in tests:
                success = test_func()
                results[test_name] = success
                
                if not success and test_name in ["Signup", "Signin"]:
                    print(f"❌ Critical test '{test_name}' failed - stopping")
                    break
                    
                time.sleep(1)  # Brief pause between tests
                
            # Generate summary
            self.generate_summary(results)
            
        except Exception as e:
            print(f"❌ Test flow error: {str(e)}")
            
    def generate_summary(self, results):
        """Generate test summary"""
        self.log_step("TEST_SUMMARY", "Unit Conversion Flow Test Results")
        
        print("📊 TEST RESULTS:")
        print("-" * 50)
        
        for test_name, success in results.items():
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {test_name}")
            
        passed = sum(1 for success in results.values() if success)
        total = len(results)
        
        print(f"\n📈 Summary: {passed}/{total} tests passed ({(passed/total*100):.1f}%)")
        
        print("\n🎯 UNIT CONVERSION FEATURES TESTED:")
        print("-" * 45)
        features = [
            "✅ Unit conversion table creation during signup",
            "✅ Pre-populated unit conversions (Chiếc, Hộp, Thùng, Kg, Gram, Tấn)",
            "✅ Custom unit conversion creation",
            "✅ Product linking to multiple unit conversions",
            "✅ Unit conversion retrieval API",
            "✅ Product search with unit conversion data"
        ]
        
        for feature in features:
            print(f"   {feature}")
            
        print("\n⚖️ UNIT CONVERSION BENEFITS:")
        print("-" * 35)
        benefits = [
            "🔄 Flexible product measurement units",
            "📊 Automatic conversion calculations",
            "📦 Multi-unit inventory management",
            "🎯 Standardized unit defaults",
            "⚡ Easy unit switching for orders",
            "📋 Comprehensive unit tracking"
        ]
        
        for benefit in benefits:
            print(f"   {benefit}")

if __name__ == "__main__":
    tester = UnitConversionFlowTester()
    tester.run_complete_flow()
