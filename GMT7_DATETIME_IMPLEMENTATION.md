# GMT+7 Datetime Implementation

## Overview

The user profile APIs now return all datetime fields converted to GMT+7 timezone (Vietnam/Indochina Time) for better user experience and consistency with local time.

## Implementation Details

### Utility Function
Created `parse_datetime_to_gmt7()` function in `app/services/user_profile_service.py`:

```python
def parse_datetime_to_gmt7(date_str):
    """Parse datetime string and convert to GMT+7 timezone"""
    if date_str:
        try:
            # Parse the datetime and convert to GMT+7
            dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            # Convert to GMT+7 timezone
            gmt_plus_7 = timezone(timedelta(hours=7))
            return dt.astimezone(gmt_plus_7)
        except:
            return None
    return None
```

### Schema Updates
Added `time_expired` field to `UserProfileResponse` schema:

```python
class UserProfileResponse(BaseModel):
    username: str
    business_name: str
    current_plan_name: Optional[str] = None
    last_login: Optional[datetime] = None
    time_expired: Optional[datetime] = None
```

### Route Updates
Updated `/user/me` endpoint to use GMT+7 conversion:

```python
user_profile = UserProfileResponse(
    username=current_user.get("username", ""),
    business_name=current_user.get("business_name", ""),
    current_plan_name=current_user.get("current_plan_name"),
    last_login=parse_datetime_to_gmt7(current_user.get("last_login")),
    time_expired=parse_datetime_to_gmt7(current_user.get("time_expired"))
)
```

## Conversion Examples

### Input (UTC)
```
2025-07-08T03:28:23.478Z
```

### Output (GMT+7)
```
2025-07-08T10:28:23.478000+07:00
```

### API Response Example
```json
{
  "status": "success",
  "message": "Lấy thông tin người dùng thành công",
  "data": {
    "username": "27102001",
    "business_name": "Công ty Cổ phần CUBABLE",
    "current_plan_name": "Nâng cao",
    "last_login": "2025-07-08T10:28:23.478000+07:00",
    "time_expired": "2026-01-01T06:59:59.999000+07:00"
  }
}
```

## Timezone Information

- **GMT+7** = UTC+07:00
- **Also known as**: ICT (Indochina Time)
- **Countries**: Vietnam, Thailand, Cambodia, Laos
- **No daylight saving time** adjustments
- **Always 7 hours ahead** of UTC

## Conversion Process

1. **Input**: ISO format datetime string with 'Z' (UTC)
2. **Parse**: Convert string to datetime object
3. **Convert**: Transform to GMT+7 timezone
4. **Output**: Timezone-aware datetime object with +07:00 offset

## Benefits

✅ **User-Friendly**: Times displayed in local Vietnamese timezone  
✅ **Consistent**: All datetime fields use same timezone  
✅ **Standard Format**: ISO 8601 with timezone offset  
✅ **Frontend Compatible**: Works with JavaScript Date objects  
✅ **Timezone Aware**: Proper timezone information included  

## Technical Details

### Files Modified
- `app/services/user_profile_service.py` - Added utility function
- `app/routes/user_profile.py` - Updated datetime parsing
- `app/schemas/user_profile.py` - Added time_expired field

### Datetime Fields Converted
- `last_login` - User's last login time
- `time_expired` - Account/token expiration time

### Error Handling
- Returns `None` for invalid datetime strings
- Graceful fallback for parsing errors
- Maintains original behavior for null values

## Usage in Frontend

### JavaScript Example
```javascript
const response = await fetch('/user/me');
const data = await response.json();

// Parse GMT+7 datetime
const lastLogin = new Date(data.data.last_login);
console.log(lastLogin.toLocaleString('vi-VN')); // Vietnamese format

// Display in local timezone
const localTime = lastLogin.toLocaleString();
console.log(localTime); // Automatically converts to user's timezone
```

### Display Examples
- **ISO Format**: `2025-07-08T10:28:23.478000+07:00`
- **Vietnamese Format**: `08/07/2025 10:28:23`
- **Relative Time**: `2 hours ago`

## Testing

Run the test script to verify conversion:
```bash
python test_datetime_gmt7.py
```

This will show:
- Original UTC times
- Converted GMT+7 times
- Timezone information
- Example API responses

## Notes

- All stored datetimes remain in UTC in the database
- Conversion happens only in API responses
- Frontend can further convert to user's local timezone if needed
- Maintains backward compatibility with existing datetime handling
