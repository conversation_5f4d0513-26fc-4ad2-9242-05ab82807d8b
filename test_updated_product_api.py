#!/usr/bin/env python3
"""
Test Updated Product API with VAT in Unit Conversions
and Removed VAT/Price from Product
"""
import json
import requests
import time

class UpdatedProductAPITester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.access_token = None
        
    def log_step(self, step_name, description):
        """Log test step"""
        print(f"\n{'='*80}")
        print(f"🧪 STEP: {step_name}")
        print(f"📝 {description}")
        print(f"{'='*80}")
        
    def log_api_call(self, method, endpoint, data=None):
        """Log API call details"""
        print(f"\n📡 API CALL: {method} {endpoint}")
        if data:
            print(f"📋 Request Data: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
    def log_response(self, response):
        """Log API response"""
        print(f"\n📊 RESPONSE:")
        print(f"Status Code: {response.status_code}")
        
        try:
            response_data = response.json()
            print(f"Response Data: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
            
            if response.status_code < 300:
                print("✅ SUCCESS")
            else:
                print("❌ FAILED")
                
            return response_data
            
        except json.JSONDecodeError:
            print(f"Raw Response: {response.text}")
            print("❌ FAILED - Invalid JSON")
            return None
            
    def test_signup(self):
        """Test user signup"""
        self.log_step("USER_SIGNUP", "Create business account")
        
        signup_data = {
            "username": "**********",
            "password": "cubable2025"
        }
        
        self.log_api_call("POST", "/auth/signup", signup_data)
        
        response = requests.post(f"{self.base_url}/auth/signup", json=signup_data)
        result = self.log_response(response)
        
        if result and response.status_code < 300:
            print(f"🏢 Business: {result.get('business_name', 'N/A')}")
            return True
        return False
        
    def test_signin(self):
        """Test user signin"""
        self.log_step("USER_SIGNIN", "Authenticate and get access token")
        
        signin_data = {
            "username": "**********",
            "password": "cubable2025"
        }
        
        self.log_api_call("POST", "/auth/signin", signin_data)
        
        response = requests.post(f"{self.base_url}/auth/signin", json=signin_data)
        result = self.log_response(response)
        
        if result and response.status_code == 200:
            self.access_token = result.get("accessToken")
            print(f"🔑 Access Token: {self.access_token[:30]}...")
            return True
        return False
        
    def test_create_unit_conversion_with_vat(self):
        """Test creating unit conversion with VAT"""
        self.log_step("CREATE_UNIT_CONVERSION_WITH_VAT", "Create unit conversion with VAT field")
        
        if not self.access_token:
            print("❌ No access token - skipping test")
            return False
            
        unit_conversion_data = {
            "name_unit": "Hộp 12 chai",
            "conversion_factor": 12,
            "unit_default": "Chai",
            "price": 180000,
            "vat": 8.0
        }
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        self.log_api_call("POST", "/unit-conversions/create-unit-conversion", unit_conversion_data)
        
        response = requests.post(f"{self.base_url}/unit-conversions/create-unit-conversion", json=unit_conversion_data, headers=headers)
        result = self.log_response(response)
        
        if result and response.status_code == 200:
            unit_data = result.get('unit_conversion_data', {})
            print(f"⚖️ Created Unit Conversion: {unit_data.get('name_unit', 'N/A')}")
            print(f"💰 Price: {unit_data.get('price', 0):,} VND")
            print(f"📊 VAT: {unit_data.get('vat', 0)}%")
            return True
        return False
        
    def test_create_product_with_vat_units(self):
        """Test creating product with unit conversions that have individual VAT rates"""
        self.log_step("CREATE_PRODUCT_WITH_VAT_UNITS", "Create product with VAT in unit conversions")
        
        if not self.access_token:
            print("❌ No access token - skipping test")
            return False
            
        product_data = {
            "product_name": "Nước ngọt Pepsi 330ml",
            "unit_conversions": [
                {
                    "name_unit": "Chai",
                    "conversion_factor": 1,
                    "unit_default": "Chai",
                    "price": 15000,
                    "vat": 10.0
                },
                {
                    "name_unit": "Lốc 24 chai",
                    "conversion_factor": 24,
                    "unit_default": "Chai",
                    "price": 350000,
                    "vat": 8.0
                },
                {
                    "name_unit": "Thùng 12 lốc",
                    "conversion_factor": 288,
                    "unit_default": "Chai",
                    "price": 4200000,
                    "vat": 5.0
                }
            ]
        }
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        self.log_api_call("POST", "/products/create-product-with-units", product_data)
        
        response = requests.post(f"{self.base_url}/products/create-product-with-units", json=product_data, headers=headers)
        result = self.log_response(response)
        
        if result and response.status_code == 200:
            product_info = result.get('product_data', {})
            created_units = result.get('created_unit_conversions', [])
            
            print(f"\n🥤 CREATED PRODUCT:")
            print(f"📦 Product Name: {product_info.get('product_name', 'N/A')}")
            print(f"🆔 Product ID: {product_info.get('product_id', 'N/A')}")
            print(f"🔗 Unit Conversions: {len(product_info.get('unit_conversions', []))}")
            
            print(f"\n⚖️ UNIT CONVERSIONS WITH INDIVIDUAL VAT:")
            for unit in created_units:
                print(f"   • {unit.get('name_unit', 'N/A')}")
                print(f"     - Factor: {unit.get('conversion_factor', 0)}")
                print(f"     - Price: {unit.get('price', 0):,} VND")
                print(f"     - VAT: {unit.get('vat', 0)}%")
                print()
            
            print(f"🔍 KEY IMPROVEMENTS:")
            print(f"   ✅ Removed unit_price from product")
            print(f"   ✅ Removed vat_rate from product")
            print(f"   ✅ Added VAT field to each unit conversion")
            print(f"   ✅ Individual VAT rates per unit type")
            print(f"   ✅ Flexible pricing and tax structure")
            
            return True
        return False
        
    def test_get_unit_conversions_with_vat(self):
        """Test getting unit conversions with VAT field"""
        self.log_step("GET_UNIT_CONVERSIONS_WITH_VAT", "Retrieve unit conversions with VAT")
        
        if not self.access_token:
            print("❌ No access token - skipping test")
            return False
            
        headers = {"Authorization": f"Bearer {self.access_token}"}
        self.log_api_call("GET", "/unit-conversions/get-unit-conversions")
        
        response = requests.get(f"{self.base_url}/unit-conversions/get-unit-conversions", headers=headers)
        result = self.log_response(response)
        
        if result and response.status_code == 200:
            unit_conversions = result.get('unit_conversions', [])
            
            print(f"\n📊 UNIT CONVERSIONS WITH VAT:")
            print(f"Total Found: {len(unit_conversions)}")
            
            for unit in unit_conversions:
                print(f"\n⚖️ {unit.get('name_unit', 'N/A')}")
                print(f"   • Factor: {unit.get('conversion_factor', 0)}")
                print(f"   • Price: {unit.get('price', 0):,} VND")
                print(f"   • VAT: {unit.get('vat', 0)}%")
                print(f"   • Default Unit: {unit.get('unit_default', 'N/A')}")
            
            return True
        return False
        
    def run_complete_test(self):
        """Run complete updated product API test"""
        print("🚀 STARTING UPDATED PRODUCT API TEST")
        print("=" * 80)
        
        try:
            # Run tests in sequence
            tests = [
                ("Signup", self.test_signup),
                ("Signin", self.test_signin),
                ("Create Unit Conversion with VAT", self.test_create_unit_conversion_with_vat),
                ("Create Product with VAT Units", self.test_create_product_with_vat_units),
                ("Get Unit Conversions with VAT", self.test_get_unit_conversions_with_vat)
            ]
            
            results = {}
            
            for test_name, test_func in tests:
                success = test_func()
                results[test_name] = success
                
                if not success and test_name in ["Signup", "Signin"]:
                    print(f"❌ Critical test '{test_name}' failed - stopping")
                    break
                    
                time.sleep(1)  # Brief pause between tests
                
            # Generate summary
            self.generate_summary(results)
            
        except Exception as e:
            print(f"❌ Test flow error: {str(e)}")
            
    def generate_summary(self, results):
        """Generate test summary"""
        self.log_step("TEST_SUMMARY", "Updated Product API Test Results")
        
        print("📊 TEST RESULTS:")
        print("-" * 50)
        
        for test_name, success in results.items():
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {test_name}")
            
        passed = sum(1 for success in results.values() if success)
        total = len(results)
        
        print(f"\n📈 Summary: {passed}/{total} tests passed ({(passed/total*100):.1f}%)")
        
        print("\n🔄 API UPDATES COMPLETED:")
        print("-" * 35)
        updates = [
            "✅ Removed unit_price from product schema",
            "✅ Removed vat_rate from product schema",
            "✅ Added vat field to unit conversion schema",
            "✅ Individual VAT rates per unit conversion",
            "✅ Flexible pricing structure per unit",
            "✅ Updated API documentation",
            "✅ Updated all service functions"
        ]
        
        for update in updates:
            print(f"   {update}")
            
        print("\n📦 BUSINESS BENEFITS:")
        print("-" * 25)
        benefits = [
            "🎯 Flexible VAT rates per unit type",
            "💰 Individual pricing per unit conversion",
            "📊 Better tax compliance options",
            "⚖️ Unit-specific tax handling",
            "🔄 Simplified product structure",
            "📋 More granular pricing control"
        ]
        
        for benefit in benefits:
            print(f"   {benefit}")

if __name__ == "__main__":
    tester = UpdatedProductAPITester()
    tester.run_complete_test()
