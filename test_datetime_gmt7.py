#!/usr/bin/env python3
"""
Test script to demonstrate GMT+7 datetime conversion
"""
from datetime import datetime, timezone, timedelta

def parse_datetime_to_gmt7(date_str):
    """Parse datetime string and convert to GMT+7 timezone"""
    if date_str:
        try:
            # Parse the datetime and convert to GMT+7
            dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            # Convert to GMT+7 timezone
            gmt_plus_7 = timezone(timedelta(hours=7))
            return dt.astimezone(gmt_plus_7)
        except:
            return None
    return None

def test_datetime_conversion():
    """Test datetime conversion to GMT+7"""
    
    print("🕐 DateTime Conversion to GMT+7 Test")
    print("=" * 60)
    
    # Test cases
    test_cases = [
        "2025-07-08T03:28:23.478Z",  # UTC time
        "2025-07-06T18:12:35.439Z",  # UTC time
        "2025-01-15T12:00:00.000Z",  # UTC time
        "2025-12-31T23:59:59.999Z"   # UTC time
    ]
    
    for i, test_time in enumerate(test_cases, 1):
        print(f"\n📅 Test Case {i}:")
        print(f"   Original (UTC): {test_time}")
        
        # Convert to GMT+7
        converted = parse_datetime_to_gmt7(test_time)
        
        if converted:
            print(f"   GMT+7 Result:   {converted.isoformat()}")
            print(f"   Timezone:       {converted.tzinfo}")
            print(f"   Formatted:      {converted.strftime('%Y-%m-%d %H:%M:%S %Z%z')}")
        else:
            print(f"   Error: Could not convert datetime")
    
    print("\n" + "=" * 60)
    print("🔍 Conversion Details:")
    print("-" * 30)
    
    details = [
        "• Input: ISO format datetime string with 'Z' (UTC)",
        "• Process: Parse to datetime object → Convert to GMT+7",
        "• Output: Datetime object with GMT+7 timezone",
        "• Time Difference: +7 hours from UTC",
        "• Format: ISO 8601 with timezone offset (+07:00)"
    ]
    
    for detail in details:
        print(f"   {detail}")
    
    print("\n" + "=" * 60)
    print("📊 API Response Example:")
    print("-" * 30)
    
    # Example API response
    example_response = {
        "status": "success",
        "message": "Lấy thông tin người dùng thành công",
        "data": {
            "username": "27102001",
            "business_name": "Công ty Cổ phần CUBABLE",
            "current_plan_name": "Nâng cao",
            "last_login": parse_datetime_to_gmt7("2025-07-08T03:28:23.478Z"),
            "time_expired": parse_datetime_to_gmt7("2025-12-31T23:59:59.999Z")
        }
    }
    
    print("   Response with GMT+7 datetimes:")
    print(f"   {{")
    print(f"     \"status\": \"{example_response['status']}\",")
    print(f"     \"message\": \"{example_response['message']}\",")
    print(f"     \"data\": {{")
    print(f"       \"username\": \"{example_response['data']['username']}\",")
    print(f"       \"business_name\": \"{example_response['data']['business_name']}\",")
    print(f"       \"current_plan_name\": \"{example_response['data']['current_plan_name']}\",")
    
    if example_response['data']['last_login']:
        print(f"       \"last_login\": \"{example_response['data']['last_login'].isoformat()}\",")
    
    if example_response['data']['time_expired']:
        print(f"       \"time_expired\": \"{example_response['data']['time_expired'].isoformat()}\"")
    
    print(f"     }}")
    print(f"   }}")
    
    print("\n" + "=" * 60)
    print("🌍 Timezone Information:")
    print("-" * 30)
    
    timezone_info = [
        "• GMT+7 = UTC+07:00",
        "• Also known as: ICT (Indochina Time)",
        "• Countries: Vietnam, Thailand, Cambodia, Laos",
        "• No daylight saving time adjustments",
        "• Always 7 hours ahead of UTC"
    ]
    
    for info in timezone_info:
        print(f"   {info}")
    
    print("\n" + "=" * 60)
    print("✅ Benefits:")
    print("-" * 30)
    
    benefits = [
        "✅ Consistent timezone for Vietnamese users",
        "✅ Automatic conversion from UTC storage",
        "✅ Proper timezone-aware datetime objects",
        "✅ ISO 8601 format with timezone offset",
        "✅ Compatible with frontend datetime libraries"
    ]
    
    for benefit in benefits:
        print(f"   {benefit}")

if __name__ == "__main__":
    test_datetime_conversion()
