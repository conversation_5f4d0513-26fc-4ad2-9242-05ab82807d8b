# Credit Reduction Feature - All APIs

## Overview

This feature automatically reduces the user's credit value by 1 when any major creation operation is completed successfully. It now covers all primary creation APIs and integrates seamlessly with existing workflows using the Teable API to update the plan status.

## Functionality

When any of the following operations are completed successfully, the system:
1. Gets the user's `current_plan` ID from the user table
2. Retrieves the current `credit_value` from the plan status table
3. Reduces the `credit_value` by 1
4. Updates the plan status record using PATCH API
5. Logs the operation for audit trail

### APIs with Credit Reduction

| API Endpoint | Service | Function | Status |
|--------------|---------|----------|--------|
| `POST /orders/create-order` | order_service.py | create_order_service() | ✅ Implemented |
| `POST /products/create-product` | product_service.py | create_product_service() | 🆕 NEW |
| `POST /products/create-product-with-units` | product_service.py | create_product_with_units_service() | 🆕 NEW |
| `POST /import-slips/create-import-slip` | import_slip_service.py | create_import_slip_service() | 🆕 NEW |

## Technical Implementation

### Files Modified

1. **`app/services/plan_status_service.py`**
   - Added `reduce_credit_value_on_order_complete()` function
   - Handles the complete credit reduction workflow

2. **`app/services/order_service.py`**
   - Integrated credit reduction into order completion flow
   - Calls credit reduction after successful delivery note creation

3. **`app/services/product_service.py`** 🆕 NEW
   - Added credit reduction to `create_product_service()`
   - Added credit reduction to `create_product_with_units_service()`
   - Calls credit reduction after successful product creation

4. **`app/services/import_slip_service.py`** 🆕 NEW
   - Added credit reduction to `create_import_slip_service()`
   - Calls credit reduction after successful import slip creation

### Function Signature

```python
async def reduce_credit_value_on_order_complete(username: str) -> bool:
    """
    Reduce credit_value by 1 when order is completed successfully
    
    Args:
        username (str): Username to reduce credit for
        
    Returns:
        bool: True if credit was reduced successfully, False otherwise
    """
```

## Data Flow

### Step 1: Query User Table
```
GET /table/{TEABLE_TABLE_ID}/record
Parameters:
- fieldKeyType: "dbFieldName"
- viewId: TEABLE_USER_VIEW_ID
- filter: username = {current_user}

Result: Extract current_plan link field ID
```

### Step 2: Get Plan Status
```
GET /table/tblL2pLkyLQgPzmCVHU/record/{plan_status_id}
Parameters:
- fieldKeyType: "dbFieldName"

Result: Get current credit_value
```

### Step 3: Update Credit Value
```
PATCH /table/tblL2pLkyLQgPzmCVHU/record/{plan_status_id}
Payload:
{
  "fieldKeyType": "dbFieldName",
  "record": {
    "fields": {
      "credit_value": current_value - 1
    }
  }
}

Result: Credit reduced by 1
```

## Integration with All Creation Flows

The credit reduction is integrated into all major creation processes:

### Order Creation Flow
1. Create order details in order detail table
2. Create main order record in order table
3. Create delivery note automatically
4. **✅ Reduce credit value by 1**
5. Return success response with order info

### Product Creation Flow 🆕 NEW
1. Create product record in product table
2. **✅ Reduce credit value by 1**
3. Return success response with product info

### Product with Units Creation Flow 🆕 NEW
1. Create unit conversion records
2. Create product record with unit links
3. **✅ Reduce credit value by 1**
4. Return success response with product and units info

### Import Slip Creation Flow 🆕 NEW
1. Create import slip details records
2. Create main import slip record
3. **✅ Reduce credit value by 1**
4. Return success response with import slip info

### Code Integration

```python
# In order_service.py after successful order creation:

# Step 6: Reduce credit value after successful order completion
credit_reduced = await reduce_credit_value_on_order_complete(current_user)
if credit_reduced:
    logger.info(f"Successfully reduced credit value for user {current_user}")
else:
    logger.warning(f"Failed to reduce credit value for user {current_user}")

# Order creation continues regardless of credit reduction result
return CreateOrderResponse(...)
```

## Error Handling

The function includes comprehensive error handling for various scenarios:

| Scenario | Action | Log Level |
|----------|--------|-----------|
| User not found | Return False | Warning |
| No current_plan field | Return False | Warning |
| Invalid current_plan format | Return False | Warning |
| Plan status not found | Return False | Error |
| Insufficient credit (≤0) | Return False | Warning |
| Update API failure | Return False | Error |
| Network/connection issues | Return False | Error |

## Key Features

- ✅ **Non-blocking**: Order succeeds even if credit reduction fails
- ✅ **Comprehensive logging**: All operations are logged for audit trail
- ✅ **Error resilient**: Handles all possible error scenarios gracefully
- ✅ **Proper API usage**: Uses dbFieldName and correct Teable endpoints
- ✅ **Link field handling**: Correctly extracts ID from link field arrays
- ✅ **Credit validation**: Checks for sufficient credit before reduction

## Configuration Requirements

### Environment Variables
- `TEABLE_TOKEN` - Bearer token for API authentication
- `TEABLE_TABLE_ID` - User table ID
- `TEABLE_USER_VIEW_ID` - View ID for user table queries

### Database Schema Requirements
- **User table** must have `current_plan` link field pointing to plan status table
- **Plan status table** (`tblL2pLkyLQgPzmCVHU`) must have `credit_value` number field

## Usage Example

The credit reduction happens automatically when creating orders through the existing API:

```bash
POST /orders/create-order
{
  "customer_id": "customer123",
  "order_details": [
    {
      "product_id": "product456",
      "unit_conversions_id": "unit789",
      "quantity": 2,
      "unit_price": 100.0,
      "vat": 10.0
    }
  ]
}
```

After successful order creation, the user's credit will be automatically reduced by 1.

## Logging

The feature provides detailed logging at different levels:

- **INFO**: Successful operations and normal flow
- **WARNING**: Non-critical issues (insufficient credit, missing fields)
- **ERROR**: Critical failures (API errors, network issues)

Example log messages:
```
INFO: Successfully reduced credit value from 100 to 99 for user: john_doe
WARNING: Insufficient credit value (0) for user: jane_doe
ERROR: Failed to get plan status: 404 - Record not found
```

## Benefits

1. **Automatic Credit Management**: No manual intervention required
2. **Integrated Workflow**: Seamlessly works with existing order process
3. **Audit Trail**: Complete logging of all credit operations
4. **Error Resilience**: System continues to work even with credit issues
5. **Real-time Updates**: Credit is updated immediately upon order completion
6. **Scalable**: Handles multiple concurrent orders efficiently
