#!/usr/bin/env python3
"""
Complete API test runner with real data scenarios
"""
import json
import requests
import time
from datetime import datetime
from test_data_scenarios import *

class CompleteAPITester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.access_token = None
        self.test_results = {}
        self.created_records = {}
        
    def log_test(self, test_name, description):
        """Log test step"""
        print(f"\n{'='*80}")
        print(f"🧪 TEST: {test_name}")
        print(f"📝 {description}")
        print(f"{'='*80}")
        
    def log_api_call(self, method, endpoint, data=None, expected_status=200):
        """Log API call details"""
        print(f"\n📡 API CALL: {method} {endpoint}")
        if data:
            print(f"📋 Request Data: {json.dumps(data, indent=2, ensure_ascii=False)}")
        print(f"⏱️ Expected Status: {expected_status}")
        
    def log_response(self, response, test_name):
        """Log API response"""
        print(f"\n📊 RESPONSE:")
        print(f"Status Code: {response.status_code}")
        
        try:
            response_data = response.json()
            print(f"Response Data: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
            
            # Store test result
            self.test_results[test_name] = {
                "status_code": response.status_code,
                "success": 200 <= response.status_code < 300,
                "response": response_data,
                "timestamp": datetime.now().isoformat()
            }
            
            if response.status_code < 300:
                print("✅ SUCCESS")
            else:
                print("❌ FAILED")
                
            return response_data
            
        except json.JSONDecodeError:
            print(f"Raw Response: {response.text}")
            self.test_results[test_name] = {
                "status_code": response.status_code,
                "success": False,
                "response": response.text,
                "timestamp": datetime.now().isoformat()
            }
            print("❌ FAILED - Invalid JSON")
            return None
            
    def test_1_signup(self):
        """Test 1: User Signup"""
        self.log_test("USER_SIGNUP", "Create new business account and workspace")
        
        business = get_test_scenario("business", 0)
        signup_data = {
            "username": business["taxcode"],
            "password": business["password"]
        }
        
        self.log_api_call("POST", "/auth/signup", signup_data, 201)
        
        response = requests.post(f"{self.base_url}/auth/signup", json=signup_data)
        result = self.log_response(response, "signup")
        
        if result and response.status_code < 300:
            print(f"🏢 Business: {result.get('business_name', 'N/A')}")
            print(f"📊 Tables Created: {len(result.get('tables', {}))}")
            self.created_records["business"] = business
            return True
        return False
        
    def test_2_signin(self):
        """Test 2: User Signin"""
        self.log_test("USER_SIGNIN", "Authenticate user and get access token")
        
        business = self.created_records.get("business", get_test_scenario("business", 0))
        signin_data = {
            "username": business["taxcode"],
            "password": business["password"]
        }
        
        self.log_api_call("POST", "/auth/signin", signin_data, 200)
        
        response = requests.post(f"{self.base_url}/auth/signin", json=signin_data)
        result = self.log_response(response, "signin")
        
        if result and response.status_code == 200:
            self.access_token = result.get("accessToken")
            print(f"🔑 Access Token: {self.access_token[:30]}...")
            return True
        return False
        
    def test_3_import_slip(self):
        """Test 3: Create Import Slip"""
        self.log_test("IMPORT_SLIP", "Create import slip to stock inventory")
        
        if not self.access_token:
            print("❌ No access token - skipping test")
            return False
            
        import_scenario = get_test_scenario("import", 0)
        
        # Convert test data to API format
        import_data = {
            "import_type": import_scenario["import_type"],
            "import_slip_details": [
                {
                    "product_id": f"recProduct{i+1:03d}",  # Simulated product IDs
                    "quantity": detail["quantity"],
                    "unit_price": detail["unit_price"],
                    "vat": detail["vat"]
                }
                for i, detail in enumerate(import_scenario["details"])
            ],
            "supplier_name": import_scenario["supplier_name"],
            "notes": import_scenario["notes"]
        }
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        self.log_api_call("POST", "/create-import-slip", import_data, 200)
        
        response = requests.post(f"{self.base_url}/create-import-slip", json=import_data, headers=headers)
        result = self.log_response(response, "import_slip")
        
        if result and response.status_code == 200:
            print(f"📥 Import Slip: {result.get('import_slip_code', 'N/A')}")
            print(f"💰 Total: {result.get('total_amount', 0):,} VND")
            self.created_records["import_slip"] = result
            return True
        return False
        
    def test_4_create_order(self):
        """Test 4: Create Customer Order"""
        self.log_test("CREATE_ORDER", "Process customer order")
        
        if not self.access_token:
            print("❌ No access token - skipping test")
            return False
            
        order_scenario = get_test_scenario("order", 0)
        
        # Convert test data to API format
        order_data = {
            "customer_name": order_scenario["customer"],
            "order_details": [
                {
                    "product_name": detail["product_name"],
                    "unit_price": detail["unit_price"],
                    "quantity": detail["quantity"],
                    "vat": detail["vat"]
                }
                for detail in order_scenario["details"]
            ],
            "detail_table_id": "tblOrderDetails123",  # Would be from user info
            "order_table_id": "tblOrders123"  # Would be from user info
        }
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        self.log_api_call("POST", "/orders/create-order", order_data, 200)
        
        response = requests.post(f"{self.base_url}/orders/create-order", json=order_data, headers=headers)
        result = self.log_response(response, "create_order")
        
        if result and response.status_code == 200:
            print(f"📦 Order: {result.get('order_number', 'N/A')}")
            self.created_records["order"] = result
            return True
        return False
        
    def test_5_delivery_note(self):
        """Test 5: Create Delivery Note"""
        self.log_test("DELIVERY_NOTE", "Create delivery note for order fulfillment")
        
        if not self.access_token:
            print("❌ No access token - skipping test")
            return False
            
        order_scenario = get_test_scenario("order", 0)
        
        # Create delivery note for the order
        delivery_data = {
            "order_id": "recOrder001",  # Would be from created order
            "customer_id": "recCustomer001",  # Would be from customer table
            "delivery_type": "Xuất bán",
            "delivery_note_details": [
                {
                    "product_id": f"recProduct{i+1:03d}",
                    "quantity": detail["quantity"],
                    "unit_price": detail["unit_price"],
                    "vat": detail["vat"]
                }
                for i, detail in enumerate(order_scenario["details"])
            ],
            "notes": f"Giao hàng cho khách hàng {order_scenario['customer']}"
        }
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        self.log_api_call("POST", "/create-delivery-note", delivery_data, 200)
        
        response = requests.post(f"{self.base_url}/create-delivery-note", json=delivery_data, headers=headers)
        result = self.log_response(response, "delivery_note")
        
        if result and response.status_code == 200:
            print(f"📤 Delivery Note: {result.get('delivery_note_code', 'N/A')}")
            print(f"🔗 Order Link: {result.get('order_id', 'N/A')}")
            self.created_records["delivery_note"] = result
            return True
        return False
        
    def test_6_generate_invoice(self):
        """Test 6: Generate Invoice"""
        self.log_test("GENERATE_INVOICE", "Generate official invoice")
        
        if not self.access_token:
            print("❌ No access token - skipping test")
            return False
            
        business = self.created_records.get("business", get_test_scenario("business", 0))
        order_scenario = get_test_scenario("order", 0)
        invoice_scenario = get_test_scenario("invoice", 0)
        
        # Calculate totals
        totals = calculate_order_totals(order_scenario["details"])
        
        invoice_data = {
            "username": business["taxcode"],
            "buyerName": invoice_scenario["buyer_info"]["buyerName"],
            "buyerTaxCode": invoice_scenario["buyer_info"]["buyerTaxCode"],
            "buyerAddressLine": invoice_scenario["buyer_info"]["buyerAddressLine"],
            "buyerEmail": invoice_scenario["buyer_info"]["buyerEmail"],
            "buyerPhone": invoice_scenario["buyer_info"]["buyerPhone"],
            "items": [
                {
                    "itemName": detail["product_name"],
                    "unitPrice": detail["unit_price"],
                    "quantity": detail["quantity"],
                    "itemTotalAmountWithoutTax": detail["unit_price"] * detail["quantity"],
                    "taxPercentage": detail["vat"],
                    "taxAmount": detail["unit_price"] * detail["quantity"] * detail["vat"] / 100,
                    "itemTotalAmountWithTax": detail["unit_price"] * detail["quantity"] * (1 + detail["vat"] / 100)
                }
                for detail in order_scenario["details"]
            ],
            "totalAmountWithoutTax": totals["total_temp"],
            "totalTaxAmount": totals["total_vat"],
            "totalAmountWithTax": totals["total_after_vat"],
            "paymentMethod": invoice_scenario["payment_method"]
        }
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        self.log_api_call("POST", "/invoices/generate-invoice", invoice_data, 200)
        
        response = requests.post(f"{self.base_url}/invoices/generate-invoice", json=invoice_data, headers=headers)
        result = self.log_response(response, "generate_invoice")
        
        if result and response.status_code == 200:
            print(f"🧾 Invoice Generated Successfully")
            self.created_records["invoice"] = result
            return True
        return False
        
    def test_7_transcription(self):
        """Test 7: Audio Transcription (Simulated)"""
        self.log_test("TRANSCRIPTION", "Voice order processing simulation")
        
        voice_test = get_test_scenario("voice", 0)
        
        print(f"🎤 Simulated Audio: '{voice_test['audio_text']}'")
        print(f"🎯 Expected Extraction: {json.dumps(voice_test['expected_extraction'], indent=2, ensure_ascii=False)}")
        
        # Simulate successful transcription
        simulated_result = {
            "language": "vi",
            "transcription": voice_test["audio_text"],
            "extracted": voice_test["expected_extraction"]
        }
        
        self.test_results["transcription"] = {
            "status_code": 200,
            "success": True,
            "response": simulated_result,
            "timestamp": datetime.now().isoformat()
        }
        
        print("✅ Transcription simulation completed")
        return True
        
    def generate_test_report(self):
        """Generate comprehensive test report"""
        self.log_test("TEST_REPORT", "Complete API Test Results Summary")
        
        print("📊 TEST RESULTS SUMMARY:")
        print("-" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"📈 Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"📊 Success Rate: {(passed_tests/total_tests*100):.1f}%")
        
        print("\n📋 DETAILED RESULTS:")
        print("-" * 30)
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            print(f"{status} {test_name.upper()}: {result['status_code']}")
            
        print("\n🔄 API FLOW VALIDATION:")
        print("-" * 35)
        
        flow_steps = [
            ("signup", "🏢 Business Registration"),
            ("signin", "🔑 User Authentication"),
            ("import_slip", "📥 Inventory Management"),
            ("create_order", "🛒 Order Processing"),
            ("delivery_note", "📤 Order Fulfillment"),
            ("generate_invoice", "🧾 Invoice Generation"),
            ("transcription", "🎤 Voice Processing")
        ]
        
        for step_key, step_name in flow_steps:
            if step_key in self.test_results:
                status = "✅" if self.test_results[step_key]["success"] else "❌"
                print(f"{status} {step_name}")
            else:
                print(f"⏭️ {step_name} (Skipped)")
                
        print("\n📦 CREATED RECORDS:")
        print("-" * 25)
        
        for record_type, data in self.created_records.items():
            print(f"📋 {record_type.upper()}: Available")
            
    def run_all_tests(self):
        """Run complete API test suite"""
        print("🚀 STARTING COMPLETE API TEST SUITE")
        print("=" * 80)
        
        try:
            # Run tests in sequence
            tests = [
                self.test_1_signup,
                self.test_2_signin,
                self.test_3_import_slip,
                self.test_4_create_order,
                self.test_5_delivery_note,
                self.test_6_generate_invoice,
                self.test_7_transcription
            ]
            
            for i, test_func in enumerate(tests, 1):
                test_func()
                if i < len(tests):
                    time.sleep(2)  # Brief pause between tests
                    
            # Generate final report
            self.generate_test_report()
            
        except Exception as e:
            print(f"❌ Test suite error: {str(e)}")
            
if __name__ == "__main__":
    tester = CompleteAPITester()
    tester.run_all_tests()
